@echo off
REM BOSS直聘爬虫系统 - Windows启动脚本
setlocal enabledelayedexpansion

REM 设置代码页为UTF-8
chcp 65001 >nul

REM 项目根目录
set "PROJECT_ROOT=%~dp0.."
cd /d "%PROJECT_ROOT%"

REM 显示横幅
call :show_banner

REM 解析命令行参数
set "CHECK_ONLY="
set "RUN_MODE="
set "INSTALL_MODE="

:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="-h" goto :show_help
if /i "%~1"=="--help" goto :show_help
if /i "%~1"=="-c" set "CHECK_ONLY=true" & shift & goto :parse_args
if /i "%~1"=="--check" set "CHECK_ONLY=true" & shift & goto :parse_args
if /i "%~1"=="-t" set "RUN_MODE=test" & shift & goto :parse_args
if /i "%~1"=="--test" set "RUN_MODE=test" & shift & goto :parse_args
if /i "%~1"=="-d" set "RUN_MODE=debug" & shift & goto :parse_args
if /i "%~1"=="--debug" set "RUN_MODE=debug" & shift & goto :parse_args
if /i "%~1"=="-H" set "RUN_MODE=headless" & shift & goto :parse_args
if /i "%~1"=="--headless" set "RUN_MODE=headless" & shift & goto :parse_args
if /i "%~1"=="-i" set "INSTALL_MODE=true" & shift & goto :parse_args
if /i "%~1"=="--install" set "INSTALL_MODE=true" & shift & goto :parse_args
echo [ERROR] 未知参数: %~1
goto :show_help

:args_done

REM 安装模式
if "%INSTALL_MODE%"=="true" (
    call :install_mode
    exit /b !errorlevel!
)

REM 开始环境检查
call :log_info "开始环境检查..."

call :check_python
if !errorlevel! neq 0 (
    call :log_error "Python环境检查失败"
    exit /b 1
)

call :check_venv
if !errorlevel! neq 0 (
    call :log_error "虚拟环境检查失败"
    exit /b 1
)

call :check_dependencies
if !errorlevel! neq 0 (
    call :log_error "依赖检查失败"
    exit /b 1
)

call :check_config
if !errorlevel! neq 0 (
    call :log_error "配置检查失败"
    exit /b 1
)

call :create_directories

REM 仅检查模式
if "%CHECK_ONLY%"=="true" (
    call :run_env_check
    exit /b !errorlevel!
)

REM 运行环境检查
call :run_env_check
if !errorlevel! neq 0 (
    call :log_error "环境检查失败，无法启动程序"
    exit /b 1
)

REM 启动程序
call :log_info "所有检查通过，启动爬虫程序..."
call :start_crawler "%RUN_MODE%"

set "exit_code=!errorlevel!"

if !exit_code! equ 0 (
    call :log_info "程序执行完成"
) else (
    call :log_error "程序执行失败，退出码: !exit_code!"
)

exit /b !exit_code!

REM ==================== 函数定义 ====================

:show_banner
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                BOSS直聘爬虫系统 - 启动脚本                     ║
echo ║                                                              ║
echo ║  🚀 自动环境检查和程序启动                                      ║
echo ║  🔧 支持多种运行模式                                           ║
echo ║  📊 实时状态监控                                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
goto :eof

:log_info
echo [INFO] %~1
goto :eof

:log_warn
echo [WARN] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

:check_python
call :log_info "检查Python环境..."

python --version >nul 2>&1
if !errorlevel! neq 0 (
    call :log_error "Python未安装或不在PATH中"
    exit /b 1
)

REM 检查Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set "PYTHON_VERSION=%%i"
call :log_info "Python版本: !PYTHON_VERSION!"

REM 检查版本是否满足要求
python -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if !errorlevel! neq 0 (
    call :log_error "需要Python 3.8+，当前版本: !PYTHON_VERSION!"
    exit /b 1
)

exit /b 0

:check_venv
if defined VIRTUAL_ENV (
    call :log_info "检测到虚拟环境: %VIRTUAL_ENV%"
    exit /b 0
)

if exist "venv\Scripts\activate.bat" (
    call :log_info "发现虚拟环境目录，激活中..."
    call venv\Scripts\activate.bat
    exit /b 0
)

call :log_warn "未检测到虚拟环境"
set /p "create_venv=是否创建虚拟环境? (y/n): "

if /i "!create_venv!"=="y" (
    call :log_info "创建虚拟环境..."
    python -m venv venv
    call venv\Scripts\activate.bat
    call :log_info "虚拟环境已激活"
)

exit /b 0

:check_dependencies
call :log_info "检查依赖包..."

if not exist "requirements.txt" (
    call :log_error "requirements.txt文件不存在"
    exit /b 1
)

REM 检查是否需要安装依赖
python -c "import requests, selenium, playwright, pandas, yaml, loguru" >nul 2>&1
if !errorlevel! neq 0 (
    call :log_warn "检测到缺失依赖包"
    set /p "install_deps=是否自动安装依赖? (y/n): "
    
    if /i "!install_deps!"=="y" (
        call :log_info "安装依赖包..."
        pip install -r requirements.txt
        
        call :log_info "安装Playwright浏览器..."
        playwright install chromium
    ) else (
        call :log_error "请手动安装依赖: pip install -r requirements.txt"
        exit /b 1
    )
)

exit /b 0

:check_config
call :log_info "检查配置文件..."

if not exist "config.yaml" (
    call :log_error "config.yaml文件不存在"
    exit /b 1
)

if not exist ".env" (
    if exist ".env.template" (
        call :log_warn ".env文件不存在，从模板创建..."
        copy ".env.template" ".env" >nul
        call :log_info "请编辑.env文件，填入必要的配置"
    ) else (
        call :log_warn ".env文件不存在"
    )
)

exit /b 0

:create_directories
call :log_info "创建必要目录..."

if not exist "logs" mkdir logs
if not exist "output" mkdir output
if not exist "temp" mkdir temp

exit /b 0

:run_env_check
call :log_info "运行环境检查..."

python run.py --check
if !errorlevel! equ 0 (
    call :log_info "环境检查通过"
    exit /b 0
) else (
    call :log_error "环境检查失败"
    exit /b 1
)

:start_crawler
set "mode=%~1"

if "%mode%"=="test" (
    call :log_info "启动测试模式..."
    python run.py --test
) else if "%mode%"=="debug" (
    call :log_info "启动调试模式..."
    python run.py --debug
) else if "%mode%"=="headless" (
    call :log_info "启动无头模式..."
    python run.py --headless
) else (
    call :log_info "启动正常模式..."
    python run.py
)

exit /b !errorlevel!

:install_mode
call :log_info "运行安装程序..."
python install.py
exit /b !errorlevel!

:show_help
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -h, --help     显示帮助信息
echo   -c, --check    仅运行环境检查
echo   -t, --test     测试模式
echo   -d, --debug    调试模式
echo   -H, --headless 无头模式
echo   -i, --install  安装依赖
echo.
echo 示例:
echo   %~nx0              # 正常启动
echo   %~nx0 --test       # 测试模式
echo   %~nx0 --check      # 环境检查
echo   %~nx0 --install    # 安装依赖
exit /b 0
