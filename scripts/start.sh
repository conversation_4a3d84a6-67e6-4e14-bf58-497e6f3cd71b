#!/bin/bash
# BOSS直聘爬虫系统 - Linux/macOS启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                BOSS直聘爬虫系统 - 启动脚本                     ║"
    echo "║                                                              ║"
    echo "║  🚀 自动环境检查和程序启动                                      ║"
    echo "║  🔧 支持多种运行模式                                           ║"
    echo "║  📊 实时状态监控                                               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        return 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    log_info "Python版本: $PYTHON_VERSION"
    
    # 检查版本是否满足要求
    if ! python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)"; then
        log_error "需要Python 3.8+，当前版本: $PYTHON_VERSION"
        return 1
    fi
    
    return 0
}

# 检查虚拟环境
check_venv() {
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        log_info "检测到虚拟环境: $VIRTUAL_ENV"
        return 0
    fi
    
    if [[ -d "venv" ]]; then
        log_info "发现虚拟环境目录，激活中..."
        source venv/bin/activate
        return 0
    fi
    
    log_warn "未检测到虚拟环境"
    read -p "是否创建虚拟环境? (y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "创建虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
        log_info "虚拟环境已激活"
    fi
    
    return 0
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖包..."
    
    if [[ ! -f "requirements.txt" ]]; then
        log_error "requirements.txt文件不存在"
        return 1
    fi
    
    # 检查是否需要安装依赖
    if ! python3 -c "import requests, selenium, playwright, pandas, yaml, loguru" &> /dev/null; then
        log_warn "检测到缺失依赖包"
        read -p "是否自动安装依赖? (y/n): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "安装依赖包..."
            pip install -r requirements.txt
            
            log_info "安装Playwright浏览器..."
            playwright install chromium
        else
            log_error "请手动安装依赖: pip install -r requirements.txt"
            return 1
        fi
    fi
    
    return 0
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [[ ! -f "config.yaml" ]]; then
        log_error "config.yaml文件不存在"
        return 1
    fi
    
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.template" ]]; then
            log_warn ".env文件不存在，从模板创建..."
            cp .env.template .env
            log_info "请编辑.env文件，填入必要的配置"
        else
            log_warn ".env文件不存在"
        fi
    fi
    
    return 0
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p output
    mkdir -p temp
    
    return 0
}

# 环境检查
run_env_check() {
    log_info "运行环境检查..."
    
    if python3 run.py --check; then
        log_info "环境检查通过"
        return 0
    else
        log_error "环境检查失败"
        return 1
    fi
}

# 启动程序
start_crawler() {
    local mode="$1"
    
    case "$mode" in
        "test")
            log_info "启动测试模式..."
            python3 run.py --test
            ;;
        "debug")
            log_info "启动调试模式..."
            python3 run.py --debug
            ;;
        "headless")
            log_info "启动无头模式..."
            python3 run.py --headless
            ;;
        *)
            log_info "启动正常模式..."
            python3 run.py
            ;;
    esac
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --check    仅运行环境检查"
    echo "  -t, --test     测试模式"
    echo "  -d, --debug    调试模式"
    echo "  -H, --headless 无头模式"
    echo "  -i, --install  安装依赖"
    echo ""
    echo "示例:"
    echo "  $0              # 正常启动"
    echo "  $0 --test       # 测试模式"
    echo "  $0 --check      # 环境检查"
    echo "  $0 --install    # 安装依赖"
}

# 安装模式
install_mode() {
    log_info "运行安装程序..."
    python3 install.py
}

# 主函数
main() {
    show_banner
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                CHECK_ONLY=true
                shift
                ;;
            -t|--test)
                RUN_MODE="test"
                shift
                ;;
            -d|--debug)
                RUN_MODE="debug"
                shift
                ;;
            -H|--headless)
                RUN_MODE="headless"
                shift
                ;;
            -i|--install)
                INSTALL_MODE=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 安装模式
    if [[ "$INSTALL_MODE" == "true" ]]; then
        install_mode
        exit $?
    fi
    
    # 环境检查步骤
    log_info "开始环境检查..."
    
    if ! check_python; then
        log_error "Python环境检查失败"
        exit 1
    fi
    
    if ! check_venv; then
        log_error "虚拟环境检查失败"
        exit 1
    fi
    
    if ! check_dependencies; then
        log_error "依赖检查失败"
        exit 1
    fi
    
    if ! check_config; then
        log_error "配置检查失败"
        exit 1
    fi
    
    create_directories
    
    # 仅检查模式
    if [[ "$CHECK_ONLY" == "true" ]]; then
        run_env_check
        exit $?
    fi
    
    # 运行环境检查
    if ! run_env_check; then
        log_error "环境检查失败，无法启动程序"
        exit 1
    fi
    
    # 启动程序
    log_info "所有检查通过，启动爬虫程序..."
    start_crawler "$RUN_MODE"
    
    exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_info "程序执行完成"
    else
        log_error "程序执行失败，退出码: $exit_code"
    fi
    
    exit $exit_code
}

# 信号处理
trap 'log_warn "收到中断信号，正在退出..."; exit 130' INT TERM

# 运行主函数
main "$@"
