#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘爬虫系统 - 自动安装脚本
自动检查和安装所有必需的依赖
"""

import sys
import os
import subprocess
import platform
from pathlib import Path
from typing import List, Tuple


def print_banner():
    """显示安装横幅"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                BOSS直聘爬虫系统 - 自动安装程序                  ║
║                                                              ║
║  🚀 一键安装所有依赖和环境配置                                  ║
║  🔧 自动检查系统兼容性                                         ║
║  📦 智能包管理和版本控制                                       ║
╚══════════════════════════════════════════════════════════════╝
    """)


def check_python_version() -> bool:
    """检查Python版本"""
    print("🔍 检查Python版本...")
    
    version = sys.version_info
    if version.major != 3 or version.minor < 8:
        print(f"❌ 需要Python 3.8+，当前版本: {version.major}.{version.minor}.{version.micro}")
        print("请升级Python版本后重试")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def run_command(command: List[str], description: str) -> bool:
    """运行命令"""
    try:
        print(f"🔧 {description}...")
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {description}出错: {e}")
        return False


def install_pip_packages() -> bool:
    """安装pip包"""
    print("📦 安装Python依赖包...")
    
    # 升级pip
    if not run_command([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], "升级pip"):
        return False
    
    # 安装requirements.txt中的包
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        if not run_command([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], "安装依赖包"):
            return False
    else:
        print("⚠️ requirements.txt文件不存在，手动安装核心包...")
        
        core_packages = [
            'requests>=2.31.0',
            'selenium>=4.15.2',
            'playwright>=1.40.0',
            'beautifulsoup4>=4.12.2',
            'lxml>=4.9.3',
            'pandas>=2.1.3',
            'openpyxl>=3.1.2',
            'loguru>=0.7.2',
            'pyyaml>=6.0.1',
            'aiohttp>=3.9.1',
            'ddddocr>=1.4.11',
            'pillow>=10.1.0',
            'fake-useragent>=1.4.0',
            'tqdm>=4.66.1',
            'python-dotenv>=1.0.0'
        ]
        
        for package in core_packages:
            if not run_command([sys.executable, '-m', 'pip', 'install', package], f"安装{package}"):
                print(f"⚠️ {package}安装失败，继续安装其他包...")
    
    return True


def install_playwright_browsers() -> bool:
    """安装Playwright浏览器"""
    print("🌐 安装Playwright浏览器...")
    
    commands = [
        ([sys.executable, '-m', 'playwright', 'install', 'chromium'], "安装Chromium浏览器"),
        ([sys.executable, '-m', 'playwright', 'install-deps', 'chromium'], "安装Chromium依赖")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            print(f"⚠️ {description}失败，可能需要手动安装")
    
    return True


def check_chrome_browser() -> bool:
    """检查Chrome浏览器"""
    print("🔍 检查Chrome浏览器...")
    
    system = platform.system().lower()
    chrome_paths = {
        'windows': [
            r'C:\Program Files\Google\Chrome\Application\chrome.exe',
            r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe'
        ],
        'darwin': [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        ],
        'linux': [
            '/usr/bin/google-chrome',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/chromium-browser'
        ]
    }
    
    paths_to_check = chrome_paths.get(system, [])
    
    for path in paths_to_check:
        if os.path.exists(path):
            print(f"✅ 找到Chrome浏览器: {path}")
            return True
    
    print("⚠️ 未找到Chrome浏览器")
    print("建议安装Google Chrome以获得最佳兼容性")
    return False


def create_directories():
    """创建必要的目录"""
    print("📁 创建项目目录...")
    
    directories = ['logs', 'output', 'temp']
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def setup_environment_file():
    """设置环境变量文件"""
    print("⚙️ 设置环境变量文件...")
    
    env_file = Path(".env")
    env_template = Path(".env.template")
    
    if not env_file.exists() and env_template.exists():
        try:
            env_file.write_text(env_template.read_text(encoding='utf-8'), encoding='utf-8')
            print("✅ 创建.env文件")
            print("📝 请编辑.env文件，填入必要的配置信息")
        except Exception as e:
            print(f"❌ 创建.env文件失败: {e}")
    else:
        print("✅ .env文件已存在")


def test_installation():
    """测试安装"""
    print("🧪 测试安装...")
    
    try:
        # 测试导入核心模块
        import requests
        import selenium
        import playwright
        import pandas
        import yaml
        import loguru
        
        print("✅ 核心模块导入成功")
        
        # 测试Playwright
        from playwright.sync_api import sync_playwright
        print("✅ Playwright可用")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def show_next_steps():
    """显示后续步骤"""
    print("""
🎉 安装完成！后续步骤：

1. 📝 配置环境变量：
   编辑 .env 文件，填入必要的配置信息
   
2. ⚙️ 检查配置文件：
   查看 config.yaml，根据需要调整配置
   
3. 🔍 环境检查：
   python run.py --check
   
4. 🧪 测试运行：
   python run.py --test
   
5. 🚀 正式运行：
   python run.py

📚 更多信息请查看 README.md 文件

⚠️ 注意事项：
- 如果使用代理，请确保代理服务正常运行
- 如果使用AI功能，请配置OpenAI API密钥
- 建议在虚拟环境中运行本程序
    """)


def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    success_count = 0
    total_steps = 6
    
    # 安装步骤
    steps = [
        (install_pip_packages, "安装Python包"),
        (install_playwright_browsers, "安装Playwright浏览器"),
        (check_chrome_browser, "检查Chrome浏览器"),
        (create_directories, "创建项目目录"),
        (setup_environment_file, "设置环境文件"),
        (test_installation, "测试安装")
    ]
    
    for step_func, step_name in steps:
        try:
            if step_func():
                success_count += 1
        except Exception as e:
            print(f"❌ {step_name}出错: {e}")
    
    # 显示结果
    print("=" * 60)
    print(f"📊 安装结果: {success_count}/{total_steps} 步骤成功")
    
    if success_count == total_steps:
        print("🎉 所有步骤完成！")
        show_next_steps()
        return 0
    elif success_count >= total_steps - 1:
        print("✅ 基本安装完成，可以尝试运行程序")
        show_next_steps()
        return 0
    else:
        print("❌ 安装存在问题，请检查错误信息")
        print("💡 建议：")
        print("  1. 检查网络连接")
        print("  2. 确保有足够的磁盘空间")
        print("  3. 检查Python环境是否正确")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装程序出错: {e}")
        sys.exit(1)
