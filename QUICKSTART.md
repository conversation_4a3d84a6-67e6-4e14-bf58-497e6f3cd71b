# 快速开始指南

🚀 **5分钟快速上手BOSS直聘爬虫系统**

## 📋 环境要求

- Python 3.8+
- Google Chrome 浏览器
- 2GB+ 可用内存

## ⚡ 快速安装

### 1. 自动安装（推荐）

```bash
# 下载项目
git clone https://github.com/your-username/boss-crawler.git
cd boss-crawler

# 一键安装所有依赖
python install.py
```

### 2. 手动安装

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium

# 配置环境变量
cp .env.template .env
# 编辑.env文件，填入必要配置
```

## 🎯 快速运行

### 基础运行

```bash
# 使用默认配置运行
python run.py

# 或使用启动脚本
./scripts/start.sh        # Linux/macOS
scripts\start.bat         # Windows
```

### 指定参数运行

```bash
# 指定城市和关键词
python run.py --cities 北京 上海 --keywords Python Java

# 测试模式（只爬取少量数据）
python run.py --test

# 调试模式
python run.py --debug

# 无头模式（不显示浏览器）
python run.py --headless
```

## ⚙️ 基础配置

### 1. 搜索配置 (config.yaml)

```yaml
boss:
  search_params:
    city_codes: ["101010100", "101020100"]  # 北京、上海
    keywords: ["Python工程师", "Java工程师"]
    salary_range: [10, 50]  # 10K-50K
```

### 2. 代理配置 (.env)

```bash
# 如果使用代理
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890
```

### 3. AI配置 (.env)

```bash
# 如果使用AI功能
OPENAI_API_KEY=sk-your-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
```

## 📊 输出结果

运行完成后，结果将保存在：

- **Excel文件**: `output/boss_jobs_YYYYMMDD_HHMMSS.xlsx`
- **日志文件**: `logs/boss_crawler_YYYYMMDD.log`

## 🔧 常见问题

### Q: 提示Chrome浏览器未找到？
A: 请安装Google Chrome浏览器，或运行 `playwright install chromium`

### Q: 遇到验证码怎么办？
A: 系统会自动处理大部分验证码，复杂验证码会提示手动处理

### Q: 爬取速度慢？
A: 可以调整配置文件中的延迟设置，但不建议设置过快

### Q: 数据不完整？
A: 检查网络连接和代理设置，确保能正常访问BOSS直聘

## 🎯 城市代码对照表

| 城市 | 代码 | 城市 | 代码 |
|------|------|------|------|
| 北京 | 101010100 | 上海 | 101020100 |
| 广州 | 101280100 | 深圳 | 101280600 |
| 成都 | 101270100 | 武汉 | 101200100 |
| 杭州 | 101210100 | 苏州 | 101190400 |

## 📞 获取帮助

```bash
# 查看所有命令选项
python run.py --help

# 环境检查
python run.py --check

# 查看版本信息
python run.py --version
```

## ⚠️ 重要提醒

1. **合法使用**: 仅用于学习和研究目的
2. **频率控制**: 不要设置过快的爬取频率
3. **数据保护**: 妥善处理获取的数据
4. **及时更新**: 定期更新系统以应对网站变化

---

🎉 **恭喜！您已经成功配置了BOSS直聘爬虫系统！**

如需更详细的配置和使用说明，请查看 [README.md](README.md) 文件。
