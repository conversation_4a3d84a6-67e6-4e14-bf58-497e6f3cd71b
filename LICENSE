MIT License

Copyright (c) 2025 BOSS直聘爬虫系统

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

================================================================================

免责声明 (Disclaimer)

本软件仅供学习和研究目的使用。使用本软件时，请遵守以下条款：

1. 法律合规性
   - 用户必须遵守所在国家和地区的法律法规
   - 不得将本软件用于任何违法或侵权活动
   - 尊重目标网站的robots.txt和服务条款

2. 使用限制
   - 本软件仅用于教育、研究和个人学习目的
   - 禁止用于商业用途或大规模数据采集
   - 不得对目标网站造成过度负载或损害

3. 数据使用
   - 爬取的数据仅供个人研究使用
   - 不得泄露、出售或传播他人隐私信息
   - 妥善保护和处理获取的数据

4. 技术责任
   - 用户自行承担使用本软件的风险
   - 开发者不对因使用本软件造成的任何损失负责
   - 用户应定期更新软件以确保安全性

5. 网站尊重
   - 合理控制爬取频率，避免对服务器造成压力
   - 遵守网站的反爬虫策略和访问限制
   - 如网站明确禁止爬取，应立即停止使用

使用本软件即表示您同意上述条款。如不同意，请勿使用本软件。

================================================================================

第三方库许可 (Third-party Licenses)

本项目使用了以下开源库，各自遵循其相应的许可证：

- Python: PSF License
- Playwright: Apache License 2.0
- Selenium: Apache License 2.0
- BeautifulSoup: MIT License
- Pandas: BSD 3-Clause License
- Requests: Apache License 2.0
- Loguru: MIT License
- OpenAI: MIT License
- ddddocr: Apache License 2.0
- Pillow: HPND License
- PyYAML: MIT License
- aiohttp: Apache License 2.0

详细的第三方许可信息请查看各库的官方文档。

================================================================================

贡献指南 (Contributing Guidelines)

欢迎为本项目做出贡献！请遵循以下指南：

1. 代码贡献
   - Fork本项目并创建功能分支
   - 确保代码符合项目的编码规范
   - 添加适当的测试和文档
   - 提交Pull Request前请先测试

2. 问题报告
   - 使用GitHub Issues报告bug
   - 提供详细的复现步骤和环境信息
   - 搜索现有issues避免重复报告

3. 功能建议
   - 通过Issues提出新功能建议
   - 详细描述功能需求和使用场景
   - 考虑功能的可行性和必要性

4. 文档改进
   - 改进README、注释和文档
   - 修正错别字和格式问题
   - 添加使用示例和最佳实践

================================================================================

联系方式 (Contact)

如有任何问题或建议，请通过以下方式联系：

- GitHub Issues: https://github.com/your-username/boss-crawler/issues
- Email: <EMAIL>

感谢您对本项目的关注和支持！
