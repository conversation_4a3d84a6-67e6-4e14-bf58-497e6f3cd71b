#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘爬虫系统 - 安装配置文件
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# 读取requirements文件
requirements = []
requirements_file = this_directory / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

setup(
    name="boss-crawler",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="基于GitHub最佳实践和AI技术的BOSS直聘全站爬虫系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/boss-crawler",
    project_urls={
        "Bug Reports": "https://github.com/your-username/boss-crawler/issues",
        "Source": "https://github.com/your-username/boss-crawler",
        "Documentation": "https://github.com/your-username/boss-crawler/blob/main/README.md",
    },
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Intended Audience :: Education",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Environment :: Console",
        "Natural Language :: Chinese (Simplified)",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "ai": ["openai>=1.3.7"],
        "full": ["openai>=1.3.7", "psutil>=5.9.0"],
    },
    entry_points={
        "console_scripts": [
            "boss-crawler=run:main",
            "boss-crawler-install=install:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.yml", "*.txt", "*.md"],
    },
    zip_safe=False,
    keywords=[
        "crawler", "spider", "scraping", "boss", "zhipin", 
        "job", "recruitment", "ai", "playwright", "automation"
    ],
    platforms=["any"],
    license="MIT",
)
