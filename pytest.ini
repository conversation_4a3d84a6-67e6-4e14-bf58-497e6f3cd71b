[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py *_test.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
    --asyncio-mode=auto

# 标记定义
markers =
    slow: 标记测试为慢速测试
    integration: 标记为集成测试
    unit: 标记为单元测试
    network: 需要网络连接的测试
    ai: 需要AI服务的测试
    browser: 需要浏览器的测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    venv
    env
    __pycache__
    .pytest_cache
    node_modules

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 异步测试配置
asyncio_mode = auto
