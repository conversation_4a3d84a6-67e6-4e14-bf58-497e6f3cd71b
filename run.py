#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘爬虫系统 - 快速启动脚本
提供多种运行模式和便捷的命令行接口
"""

import asyncio
import sys
import argparse
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import BossCrawlerApp


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="BOSS直聘全站爬虫系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run.py                          # 使用默认配置运行
  python run.py --cities 北京 上海        # 指定城市
  python run.py --keywords Python Java   # 指定关键词
  python run.py --test                   # 测试模式
  python run.py --config custom.yaml     # 使用自定义配置
  python run.py --check                  # 仅检查环境
        """
    )
    
    # 基本参数
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    parser.add_argument(
        '--cities',
        nargs='+',
        help='指定城市列表 (如: 北京 上海 广州)'
    )
    
    parser.add_argument(
        '--keywords',
        nargs='+',
        help='指定搜索关键词 (如: Python Java 前端)'
    )
    
    parser.add_argument(
        '--salary-min',
        type=int,
        help='最低薪资 (K)'
    )
    
    parser.add_argument(
        '--salary-max',
        type=int,
        help='最高薪资 (K)'
    )
    
    # 运行模式
    parser.add_argument(
        '--test',
        action='store_true',
        help='测试模式 (只爬取少量数据)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='调试模式 (显示详细日志)'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='无头模式 (不显示浏览器窗口)'
    )
    
    parser.add_argument(
        '--no-ai',
        action='store_true',
        help='禁用AI功能'
    )
    
    parser.add_argument(
        '--no-proxy',
        action='store_true',
        help='禁用代理'
    )
    
    # 工具功能
    parser.add_argument(
        '--check',
        action='store_true',
        help='仅检查环境，不运行爬虫'
    )
    
    parser.add_argument(
        '--version',
        action='store_true',
        help='显示版本信息'
    )
    
    return parser.parse_args()


def show_version():
    """显示版本信息"""
    print("""
BOSS直聘全站爬虫系统 v1.0.0

基于GitHub最佳实践和AI技术的全面爬虫解决方案

技术栈:
  - Python 3.8+
  - Playwright (浏览器自动化)
  - OpenAI GPT-4o-mini (AI分析)
  - ddddocr (验证码识别)
  - aiohttp (异步HTTP)

作者: AI Assistant
日期: 2025-07-03
许可: MIT License
    """)


async def check_environment_only():
    """仅检查环境"""
    from src.utils.environment_check import check_environment
    
    print("🔍 正在检查系统环境...")
    success = check_environment()
    
    if success:
        print("\n✅ 环境检查通过，系统可以正常运行！")
        return 0
    else:
        print("\n❌ 环境检查失败，请解决问题后重试。")
        return 1


def apply_command_line_overrides(config_manager, args):
    """应用命令行参数覆盖配置"""
    try:
        # 城市覆盖
        if args.cities:
            # 城市名称到代码的映射
            city_mapping = {
                '北京': '101010100',
                '上海': '101020100',
                '广州': '101280100',
                '深圳': '101280600',
                '成都': '101270100',
                '武汉': '101200100',
                '杭州': '101210100',
                '苏州': '101190400'
            }
            
            city_codes = []
            for city in args.cities:
                if city in city_mapping:
                    city_codes.append(city_mapping[city])
                else:
                    logger.warning(f"⚠️ 未知城市: {city}")
            
            if city_codes:
                config_manager.set('boss.search_params.city_codes', city_codes)
                logger.info(f"🏙️ 设置城市: {args.cities}")
        
        # 关键词覆盖
        if args.keywords:
            config_manager.set('boss.search_params.keywords', args.keywords)
            logger.info(f"🔍 设置关键词: {args.keywords}")
        
        # 薪资范围覆盖
        if args.salary_min or args.salary_max:
            current_range = config_manager.get('boss.search_params.salary_range', [10, 50])
            new_range = [
                args.salary_min or current_range[0],
                args.salary_max or current_range[1]
            ]
            config_manager.set('boss.search_params.salary_range', new_range)
            logger.info(f"💰 设置薪资范围: {new_range[0]}-{new_range[1]}K")
        
        # 测试模式
        if args.test:
            config_manager.set('debug.test_mode', True)
            config_manager.set('debug.test_limit', 10)
            logger.info("🧪 启用测试模式")
        
        # 调试模式
        if args.debug:
            config_manager.set('app.debug', True)
            config_manager.set('app.log_level', 'DEBUG')
            logger.info("🐛 启用调试模式")
        
        # 无头模式
        if args.headless:
            config_manager.set('anti_crawler.browser.headless', True)
            logger.info("👻 启用无头模式")
        
        # 禁用AI
        if args.no_ai:
            config_manager.set('ai.enabled', False)
            logger.info("🚫 禁用AI功能")
        
        # 禁用代理
        if args.no_proxy:
            config_manager.set('proxy.enabled', False)
            logger.info("🚫 禁用代理")
        
    except Exception as e:
        logger.error(f"❌ 应用命令行参数失败: {e}")


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 显示版本信息
        if args.version:
            show_version()
            return 0
        
        # 仅检查环境
        if args.check:
            return await check_environment_only()
        
        # 显示启动信息
        print("""
╔══════════════════════════════════════════════════════════════╗
║                    BOSS直聘全站爬虫系统                        ║
║                                                              ║
║  🚀 基于GitHub最佳实践和AI技术的全面爬虫解决方案                ║
║  🎯 支持全国所有城市，智能反爬虫，实时数据保存                   ║
║  💡 集成本地代理，AI智能匹配，Excel格式输出                     ║
║                                                              ║
║  版本: 1.0.0  |  作者: AI Assistant  |  日期: 2025-07-03      ║
╚══════════════════════════════════════════════════════════════╝
        """)
        
        # 创建应用实例
        app = BossCrawlerApp()
        
        # 如果指定了自定义配置文件
        if args.config != 'config.yaml':
            from src.core.config_manager import ConfigManager
            app.config_manager = ConfigManager(args.config)
        
        # 应用命令行参数覆盖
        apply_command_line_overrides(app.config_manager, args)
        
        # 运行应用
        success = await app.run()
        
        if success:
            print("\n🎉 爬取任务完成！请查看输出文件。")
            return 0
        else:
            print("\n❌ 爬取任务失败！请查看日志了解详情。")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        return 1


if __name__ == "__main__":
    try:
        # 设置事件循环策略（Windows兼容性）
        if sys.platform.startswith('win'):
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行主程序
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序启动失败: {e}")
        sys.exit(1)
