# BOSS直聘爬虫系统 - Docker Compose配置
version: '3.8'

services:
  # 主爬虫服务
  boss-crawler:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: boss-crawler
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
    volumes:
      # 挂载配置文件
      - ../config.yaml:/app/config.yaml:ro
      - ../.env:/app/.env:ro
      # 挂载输出目录
      - ../output:/app/output
      - ../logs:/app/logs
      # 挂载临时目录
      - ../temp:/app/temp
    networks:
      - crawler-network
    depends_on:
      - redis
      - mongodb
    command: ["--headless"]
    
  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: boss-crawler-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - crawler-network
    command: redis-server --appendonly yes
    
  # MongoDB数据库服务
  mongodb:
    image: mongo:7
    container_name: boss-crawler-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=boss_crawler
    volumes:
      - mongodb-data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - crawler-network
      
  # 代理服务（可选）
  proxy:
    image: nginx:alpine
    container_name: boss-crawler-proxy
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - crawler-network
    depends_on:
      - boss-crawler
      
  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: boss-crawler-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - crawler-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      
  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: boss-crawler-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - crawler-network
    depends_on:
      - prometheus

# 网络配置
networks:
  crawler-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  redis-data:
    driver: local
  mongodb-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# 扩展配置
x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

# 应用默认日志配置到所有服务
services:
  boss-crawler:
    logging: *default-logging
  redis:
    logging: *default-logging
  mongodb:
    logging: *default-logging
  proxy:
    logging: *default-logging
  prometheus:
    logging: *default-logging
  grafana:
    logging: *default-logging
