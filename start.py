#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘爬虫系统 - 简易启动文件
一键启动，自动安装依赖，开始爬取
"""

import sys
import os
import subprocess
import asyncio
from pathlib import Path

def print_banner():
    """显示启动横幅"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                BOSS直聘爬虫系统 v1.0.0                        ║
║                                                              ║
║  🚀 基于GitHub最佳实践和AI技术的全面爬虫解决方案                ║
║  🎯 支持全国所有城市，智能反爬虫，实时数据保存                   ║
║  💡 集成本地代理，AI智能匹配，Excel格式输出                     ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_python():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8+，当前版本:", sys.version)
        return False
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖包...")
    
    try:
        # 检查是否已安装核心包
        import requests, selenium, playwright, pandas, yaml, loguru
        print("✅ 核心依赖已安装")
        return True
    except ImportError:
        pass
    
    # 安装依赖
    try:
        print("🔧 正在安装依赖包...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True, capture_output=True)
        
        print("🌐 正在安装Playwright浏览器...")
        subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'], 
                      check=True, capture_output=True)
        
        print("✅ 依赖安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def create_directories():
    """创建必要目录"""
    directories = ['logs', 'output', 'temp']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✅ 目录创建完成")

def setup_environment():
    """设置环境"""
    env_file = Path(".env")
    env_template = Path(".env.template")
    
    if not env_file.exists() and env_template.exists():
        env_file.write_text(env_template.read_text(encoding='utf-8'), encoding='utf-8')
        print("✅ 环境变量文件已创建")
    
    if not Path("config.yaml").exists():
        print("❌ config.yaml文件不存在")
        return False
    
    return True

async def run_crawler():
    """运行爬虫"""
    try:
        print("🚀 启动BOSS直聘爬虫...")
        
        # 导入主程序
        from main import BossCrawlerApp
        
        # 创建应用实例
        app = BossCrawlerApp()
        
        # 运行爬虫
        success = await app.run()
        
        if success:
            print("\n🎉 爬取任务完成！请查看output目录中的Excel文件。")
            return True
        else:
            print("\n❌ 爬取任务失败！请查看logs目录中的日志文件。")
            return False
            
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        return False

def main():
    """主函数"""
    try:
        print_banner()
        
        # 检查Python版本
        if not check_python():
            input("按Enter键退出...")
            return 1
        
        # 安装依赖
        if not install_dependencies():
            input("按Enter键退出...")
            return 1
        
        # 创建目录
        create_directories()
        
        # 设置环境
        if not setup_environment():
            input("按Enter键退出...")
            return 1
        
        print("\n" + "="*60)
        print("🎯 准备工作完成，开始爬取数据...")
        print("💡 提示：程序运行过程中请勿关闭窗口")
        print("📊 爬取进度和结果将实时显示")
        print("="*60 + "\n")
        
        # 运行爬虫
        success = asyncio.run(run_crawler())
        
        print("\n" + "="*60)
        if success:
            print("✅ 程序执行完成")
            print("📁 输出文件位置: output/")
            print("📋 日志文件位置: logs/")
        else:
            print("❌ 程序执行失败")
            print("🔍 请查看日志文件了解详情")
        print("="*60)
        
        input("\n按Enter键退出...")
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        input("按Enter键退出...")
        return 1
    except Exception as e:
        print(f"\n❌ 程序启动失败: {e}")
        input("按Enter键退出...")
        return 1

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    exit_code = main()
    sys.exit(exit_code)
