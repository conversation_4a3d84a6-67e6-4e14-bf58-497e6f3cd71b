# BOSS直聘爬虫系统配置文件
# ================================

# 基础配置
app:
  name: "BOSS直聘全站爬虫系统"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# 代理配置
proxy:
  enabled: true
  http_proxy: "http://127.0.0.1:7890"
  https_proxy: "http://127.0.0.1:7890"
  timeout: 30
  retry_times: 3

# BOSS直聘配置
boss:
  base_url: "https://www.zhipin.com"
  search_url: "https://www.zhipin.com/web/geek/job"
  
  # 搜索参数
  search_params:
    # 城市代码（支持多城市）
    city_codes: ["101010100", "101020100", "*********", "*********"]  # 北京、上海、广州、深圳
    
    # 关键词（会依次搜索）
    keywords: 
      - "Python工程师"
      - "Java工程师" 
      - "前端工程师"
      - "数据分析师"
      - "产品经理"
      - "运营专员"
    
    # 薪资范围（K为单位）
    salary_range: [10, 50]  # 10K-50K
    
    # 工作经验
    experience: ["1-3年", "3-5年", "5-10年"]
    
    # 学历要求
    education: ["本科", "硕士"]
    
    # 公司规模
    company_scale: ["100-499人", "500-999人", "1000-9999人", "10000人以上"]
    
    # 融资阶段
    financing_stage: ["A轮", "B轮", "C轮", "D轮及以上", "已上市"]

# 反爬虫配置
anti_crawler:
  # 用户代理轮换
  user_agents:
    enabled: true
    random: true
    
  # 请求间隔（秒）
  request_delay:
    min: 2
    max: 5
    
  # 浏览器配置
  browser:
    headless: false  # 调试时设为false
    window_size: [1920, 1080]
    disable_images: true
    disable_css: false
    
  # 验证码处理
  captcha:
    enabled: true
    ddddocr_enabled: true
    manual_solve: true  # 遇到复杂验证码时手动处理
    
  # 设备指纹
  fingerprint:
    canvas_enabled: true
    webgl_enabled: true
    audio_enabled: true

# 数据提取配置
data_extraction:
  # 必须提取的字段
  required_fields:
    - "job_title"        # 岗位名称
    - "salary_info"      # 薪资情况
    - "benefits"         # 福利待遇
    - "experience_req"   # 工作经验要求
    - "education_req"    # 学历要求
    - "job_description"  # 职位详细要求
    - "company_info"     # 公司介绍
    - "work_location"    # 工作地址
    - "job_url"          # 岗位详情页URL
    
  # 数据验证
  validation:
    min_salary: 5        # 最低薪资（K）
    max_salary: 100      # 最高薪资（K）
    url_validation: true # 验证URL可访问性
    
  # 过滤条件
  filters:
    exclude_keywords:    # 排除关键词
      - "外包"
      - "外派" 
      - "实习"
      - "兼职"
    exclude_companies:   # 排除公司
      - "某某外包公司"
    min_company_size: 50 # 最小公司规模

# 数据存储配置
storage:
  # Excel输出
  excel:
    enabled: true
    filename: "boss_jobs_{date}.xlsx"
    sheet_name: "招聘信息"
    auto_save_interval: 50  # 每50条数据自动保存
    
  # 实时保存
  realtime_save:
    enabled: true
    backup_interval: 100    # 每100条备份一次
    
  # 数据库（可选）
  database:
    enabled: false
    type: "sqlite"
    path: "boss_jobs.db"

# AI配置
ai:
  enabled: true
  
  # OpenAI配置
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    model: "gpt-4o-mini"
    
  # 智能匹配
  smart_matching:
    enabled: true
    threshold: 0.7  # 匹配度阈值
    
  # 自动分析
  auto_analysis:
    job_quality_score: true
    salary_analysis: true
    company_rating: true

# 监控和通知
monitoring:
  # 企业微信通知
  wechat:
    enabled: false
    webhook_url: "${WECHAT_WEBHOOK_URL}"
    
  # 邮件通知
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"
    
  # 进度监控
  progress:
    show_progress_bar: true
    log_interval: 10  # 每10条记录日志

# 性能配置
performance:
  # 并发设置
  concurrency:
    max_workers: 3      # 最大并发数
    semaphore_limit: 5  # 信号量限制
    
  # 内存管理
  memory:
    max_memory_mb: 1024 # 最大内存使用（MB）
    gc_interval: 100    # 垃圾回收间隔
    
  # 超时设置
  timeouts:
    page_load: 30       # 页面加载超时
    element_wait: 10    # 元素等待超时
    request_timeout: 20 # 请求超时

# 调试配置
debug:
  save_html: false      # 保存页面HTML
  save_screenshots: true # 保存截图
  verbose_logging: true  # 详细日志
  test_mode: false      # 测试模式（只爬取少量数据）
  test_limit: 10        # 测试模式下的数据限制
