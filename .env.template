# BOSS直聘爬虫系统环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ================================
# AI配置 (可选)
# ================================

# OpenAI API配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# 如云API配置 (推荐的代理服务)
# OPENAI_BASE_URL=https://api.ruyun.fun/
# OPENAI_API_KEY=your-ruyun-api-key

# ================================
# 通知配置 (可选)
# ================================

# 企业微信机器人通知
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key-here

# 邮件通知配置
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-email-password

# ================================
# 代理配置
# ================================

# 本地代理设置 (如果使用代理)
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# 代理池配置 (可选)
# PROXY_POOL=http://proxy1:port,http://proxy2:port

# ================================
# 数据库配置 (可选)
# ================================

# MongoDB配置
MONGODB_URL=mongodb://localhost:27017/boss_crawler

# MySQL配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your-password
MYSQL_DATABASE=boss_crawler

# ================================
# 其他配置
# ================================

# 调试模式
DEBUG=false

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 最大并发数
MAX_CONCURRENT_REQUESTS=3

# 请求超时时间 (秒)
REQUEST_TIMEOUT=30
