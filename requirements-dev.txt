# 开发环境依赖包
# 包含生产环境依赖和开发工具

# 生产环境依赖
-r requirements.txt

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0

# 代码质量工具
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
pylint>=2.17.0

# 文档工具
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# 开发工具
ipython>=8.14.0
jupyter>=1.0.0
pre-commit>=3.3.0

# 性能分析
memory-profiler>=0.61.0
line-profiler>=4.1.0
py-spy>=0.3.14

# 安全检查
bandit>=1.7.5
safety>=2.3.0

# 类型检查
types-requests>=2.31.0
types-PyYAML>=6.0.0
types-pillow>=10.0.0

# 调试工具
pdb++>=0.10.3
icecream>=2.1.3

# 构建工具
build>=0.10.0
twine>=4.0.0
wheel>=0.41.0

# 环境管理
python-dotenv>=1.0.0
virtualenv>=20.24.0

# 数据分析（开发时使用）
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# API文档
fastapi>=0.100.0
uvicorn>=0.23.0

# 监控工具
psutil>=5.9.0
rich>=13.5.0
