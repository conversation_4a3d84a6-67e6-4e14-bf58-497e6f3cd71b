#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器测试
"""

import pytest
import tempfile
import yaml
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.yaml"
        self.env_file = Path(self.temp_dir) / ".env"
        
        # 创建测试配置文件
        test_config = {
            'app': {
                'name': 'test_app',
                'debug': True,
                'log_level': 'DEBUG'
            },
            'boss': {
                'base_url': 'https://www.zhipin.com',
                'search_params': {
                    'city_codes': ['101010100'],
                    'keywords': ['Python'],
                    'salary_range': [10, 30]
                }
            },
            'proxy': {
                'enabled': True,
                'http_proxy': 'http://127.0.0.1:7890'
            },
            'ai': {
                'enabled': True,
                'openai': {
                    'api_key': '${OPENAI_API_KEY}',
                    'model': 'gpt-4o-mini'
                }
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f, default_flow_style=False, allow_unicode=True)
        
        # 创建测试环境变量文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            f.write('OPENAI_API_KEY=test-api-key\n')
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_loading(self):
        """测试配置加载"""
        # 临时设置环境变量
        os.environ['OPENAI_API_KEY'] = 'test-api-key'
        
        config_manager = ConfigManager(str(self.config_file))
        
        assert config_manager.config is not None
        assert config_manager.get('app.name') == 'test_app'
        assert config_manager.get('app.debug') is True
        
        # 清理环境变量
        if 'OPENAI_API_KEY' in os.environ:
            del os.environ['OPENAI_API_KEY']
    
    def test_environment_variable_replacement(self):
        """测试环境变量替换"""
        os.environ['OPENAI_API_KEY'] = 'test-api-key'
        
        config_manager = ConfigManager(str(self.config_file))
        
        # 检查环境变量是否被正确替换
        api_key = config_manager.get('ai.openai.api_key')
        assert api_key == 'test-api-key'
        
        # 清理环境变量
        if 'OPENAI_API_KEY' in os.environ:
            del os.environ['OPENAI_API_KEY']
    
    def test_get_method(self):
        """测试get方法"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 测试存在的键
        assert config_manager.get('app.name') == 'test_app'
        assert config_manager.get('boss.search_params.city_codes') == ['101010100']
        
        # 测试不存在的键
        assert config_manager.get('nonexistent.key') is None
        assert config_manager.get('nonexistent.key', 'default') == 'default'
    
    def test_set_method(self):
        """测试set方法"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 设置新值
        config_manager.set('app.version', '1.0.0')
        assert config_manager.get('app.version') == '1.0.0'
        
        # 设置嵌套值
        config_manager.set('new.nested.key', 'value')
        assert config_manager.get('new.nested.key') == 'value'
    
    def test_helper_methods(self):
        """测试辅助方法"""
        config_manager = ConfigManager(str(self.config_file))
        
        # 测试城市代码获取
        city_codes = config_manager.get_city_codes()
        assert city_codes == ['101010100']
        
        # 测试关键词获取
        keywords = config_manager.get_keywords()
        assert keywords == ['Python']
        
        # 测试薪资范围获取
        salary_range = config_manager.get_salary_range()
        assert salary_range == [10, 30]
        
        # 测试代理状态检查
        assert config_manager.is_proxy_enabled() is True
        
        # 测试AI状态检查
        assert config_manager.is_ai_enabled() is True
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试正常配置
        config_manager = ConfigManager(str(self.config_file))
        assert config_manager.config is not None
        
        # 测试缺少必需节的配置
        invalid_config = {'app': {'name': 'test'}}
        invalid_config_file = Path(self.temp_dir) / "invalid_config.yaml"
        
        with open(invalid_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(invalid_config, f)
        
        with pytest.raises(ValueError):
            ConfigManager(str(invalid_config_file))
    
    def test_proxy_config(self):
        """测试代理配置"""
        config_manager = ConfigManager(str(self.config_file))
        
        proxy_config = config_manager.get_proxy_config()
        expected = {
            'http': 'http://127.0.0.1:7890',
            'https': 'http://127.0.0.1:7890'
        }
        assert proxy_config == expected
        
        # 测试禁用代理
        config_manager.set('proxy.enabled', False)
        assert config_manager.get_proxy_config() == {}
    
    def test_ai_config(self):
        """测试AI配置"""
        config_manager = ConfigManager(str(self.config_file))
        
        ai_config = config_manager.get_ai_config()
        assert 'enabled' in ai_config
        assert 'openai' in ai_config
        
        # 测试禁用AI
        config_manager.set('ai.enabled', False)
        assert config_manager.is_ai_enabled() is False


if __name__ == '__main__':
    pytest.main([__file__])
