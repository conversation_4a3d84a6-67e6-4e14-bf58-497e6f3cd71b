#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL验证器测试
"""

import pytest
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.url_validator import URLValidator


class TestURLValidator:
    """URL验证器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.validator = URLValidator(timeout=5, max_retries=2)
    
    def test_is_valid_url(self):
        """测试URL格式验证"""
        # 有效URL
        assert self.validator.is_valid_url('https://www.zhipin.com') is True
        assert self.validator.is_valid_url('http://example.com/path') is True
        assert self.validator.is_valid_url('https://www.zhipin.com/job_detail/123.html') is True
        
        # 无效URL
        assert self.validator.is_valid_url('') is False
        assert self.validator.is_valid_url(None) is False
        assert self.validator.is_valid_url('invalid-url') is False
        assert self.validator.is_valid_url('ftp://example.com') is False
        assert self.validator.is_valid_url('javascript:alert(1)') is False
    
    def test_is_valid_boss_job_url(self):
        """测试BOSS直聘岗位URL验证"""
        # 有效的BOSS岗位URL
        valid_urls = [
            'https://www.zhipin.com/job_detail/123abc.html',
            'https://www.zhipin.com/job_detail/456def789.html'
        ]
        
        for url in valid_urls:
            assert self.validator.is_valid_boss_job_url(url) is True
        
        # 无效的BOSS岗位URL
        invalid_urls = [
            'https://other-site.com/job_detail/123.html',
            'https://www.zhipin.com/company/123.html',
            'https://www.zhipin.com/job_detail/',
            'https://www.zhipin.com/job_detail/123',  # 缺少.html
            'http://www.zhipin.com/job_detail/123.html',  # http而非https
            ''
        ]
        
        for url in invalid_urls:
            assert self.validator.is_valid_boss_job_url(url) is False
    
    @pytest.mark.asyncio
    async def test_validate_url_accessibility_mock(self):
        """测试URL可访问性验证（模拟）"""
        # 由于这是单元测试，我们不进行真实的网络请求
        # 这里主要测试方法的结构和逻辑
        
        # 测试无效URL
        accessible, info = await self.validator.validate_url_accessibility('invalid-url')
        assert accessible is False
        assert 'error' in info
    
    def test_extract_urls_from_text(self):
        """测试从文本中提取URL"""
        text = """
        这是一些包含URL的文本：
        https://www.zhipin.com/job_detail/123.html
        http://example.com/path
        https://www.google.com
        无效的链接：javascript:alert(1)
        另一个链接：https://www.zhipin.com/company/456.html
        """
        
        urls = self.validator.extract_urls_from_text(text)
        
        # 检查提取的URL数量和内容
        assert len(urls) >= 3
        assert 'https://www.zhipin.com/job_detail/123.html' in urls
        assert 'http://example.com/path' in urls
        assert 'https://www.google.com' in urls
        assert 'https://www.zhipin.com/company/456.html' in urls
        
        # 确保无效URL不被提取
        assert 'javascript:alert(1)' not in urls
    
    def test_normalize_url(self):
        """测试URL标准化"""
        # 测试基本标准化
        normalized = self.validator.normalize_url('https://www.zhipin.com/job_detail/123.html#section')
        assert normalized == 'https://www.zhipin.com/job_detail/123.html'
        
        # 测试相对URL转换
        base_url = 'https://www.zhipin.com'
        relative_url = '/job_detail/123.html'
        normalized = self.validator.normalize_url(relative_url, base_url)
        assert normalized == 'https://www.zhipin.com/job_detail/123.html'
        
        # 测试带查询参数的URL
        url_with_query = 'https://www.zhipin.com/job_detail/123.html?param=value'
        normalized = self.validator.normalize_url(url_with_query)
        assert normalized == 'https://www.zhipin.com/job_detail/123.html?param=value'
        
        # 测试空URL
        assert self.validator.normalize_url('') == ''
        assert self.validator.normalize_url(None) == ''
    
    @pytest.mark.asyncio
    async def test_filter_valid_urls(self):
        """测试过滤有效URL"""
        test_urls = [
            'https://www.zhipin.com/job_detail/123.html',
            'invalid-url',
            'https://www.google.com',
            '',
            'ftp://example.com',
            'https://www.zhipin.com/company/456.html'
        ]
        
        # 仅格式检查
        valid_urls = await self.validator.filter_valid_urls(test_urls, check_accessibility=False)
        
        # 检查结果
        assert 'https://www.zhipin.com/job_detail/123.html' in valid_urls
        assert 'https://www.google.com' in valid_urls
        assert 'https://www.zhipin.com/company/456.html' in valid_urls
        assert 'invalid-url' not in valid_urls
        assert '' not in valid_urls
        assert 'ftp://example.com' not in valid_urls
    
    def test_get_validation_stats(self):
        """测试获取验证统计信息"""
        stats = self.validator.get_validation_stats()
        
        assert 'cache_size' in stats
        assert 'successful_validations' in stats
        assert 'failed_validations' in stats
        assert 'cache_hit_rate' in stats
        assert 'timeout' in stats
        assert 'max_retries' in stats
        
        assert stats['timeout'] == 5
        assert stats['max_retries'] == 2
    
    def test_clear_cache(self):
        """测试清理缓存"""
        # 添加一些缓存数据
        self.validator.validation_cache['test_url'] = {
            'accessible': True,
            'info': {'status_code': 200},
            'timestamp': 1234567890
        }
        
        assert len(self.validator.validation_cache) > 0
        
        # 清理缓存
        self.validator.clear_cache()
        
        assert len(self.validator.validation_cache) == 0
    
    @pytest.mark.asyncio
    async def test_context_manager(self):
        """测试异步上下文管理器"""
        async with URLValidator() as validator:
            assert validator.session is not None
            
            # 测试基本功能
            assert validator.is_valid_url('https://www.zhipin.com') is True
        
        # 上下文退出后，session应该被清理
        # 注意：这里不能直接检查session是否为None，因为cleanup可能是异步的
    
    def test_url_patterns(self):
        """测试URL模式匹配"""
        # 测试BOSS岗位URL模式
        boss_job_urls = [
            'https://www.zhipin.com/job_detail/abc123.html',
            'https://www.zhipin.com/job_detail/xyz789def.html'
        ]
        
        for url in boss_job_urls:
            assert self.validator.is_valid_boss_job_url(url) is True
        
        # 测试非BOSS岗位URL
        non_boss_urls = [
            'https://www.zhipin.com/company/123.html',
            'https://www.zhipin.com/web/geek/job',
            'https://other-site.com/job_detail/123.html'
        ]
        
        for url in non_boss_urls:
            if url.startswith('https://www.zhipin.com/job_detail/'):
                continue  # 这些可能是有效的岗位URL
            assert self.validator.is_valid_boss_job_url(url) is False


class TestURLValidatorIntegration:
    """URL验证器集成测试"""
    
    @pytest.mark.asyncio
    async def test_batch_validation_structure(self):
        """测试批量验证的结构"""
        validator = URLValidator()
        
        test_urls = [
            'https://www.zhipin.com/job_detail/123.html',
            'https://www.google.com'
        ]
        
        # 由于这是单元测试，我们不进行真实的网络请求
        # 主要测试方法调用不会出错
        try:
            await validator.initialize()
            # 这里可以添加更多的结构测试
            await validator.cleanup()
        except Exception as e:
            # 在测试环境中，网络相关的初始化可能会失败，这是正常的
            pass


if __name__ == '__main__':
    pytest.main([__file__])
