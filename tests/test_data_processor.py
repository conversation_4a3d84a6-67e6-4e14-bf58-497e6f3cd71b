#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器测试
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.data.data_processor import DataProcessor


class TestDataProcessor:
    """数据处理器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = {
            'required_fields': [
                'job_title', 'salary_info', 'benefits', 'experience_req',
                'education_req', 'job_description', 'company_info',
                'work_location', 'job_url'
            ],
            'validation': {
                'min_salary': 5,
                'max_salary': 100,
                'url_validation': True
            },
            'filters': {
                'exclude_keywords': ['外包', '实习'],
                'exclude_companies': ['测试外包公司'],
                'min_company_size': 50
            }
        }
        
        self.processor = DataProcessor(self.config)
    
    def test_validate_job_data_valid(self):
        """测试有效数据验证"""
        valid_job_data = {
            'job_title': 'Python工程师',
            'salary_info': '15-25K',
            'benefits': '五险一金',
            'experience_req': '3-5年',
            'education_req': '本科',
            'job_description': '负责后端开发工作',
            'company_info': '某科技公司，100-500人',
            'work_location': '北京市朝阳区',
            'job_url': 'https://www.zhipin.com/job_detail/123.html'
        }
        
        assert self.processor.validate_job_data(valid_job_data) is True
    
    def test_validate_job_data_missing_fields(self):
        """测试缺少必需字段的数据"""
        invalid_job_data = {
            'job_title': 'Python工程师',
            'salary_info': '15-25K'
            # 缺少其他必需字段
        }
        
        assert self.processor.validate_job_data(invalid_job_data) is False
    
    def test_validate_salary(self):
        """测试薪资验证"""
        # 有效薪资
        assert self.processor._validate_salary('15-25K') is True
        assert self.processor._validate_salary('8-12K') is True
        
        # 无效薪资
        assert self.processor._validate_salary('') is False
        assert self.processor._validate_salary('面议') is False
        assert self.processor._validate_salary('3-5K') is False  # 低于最低限制
        assert self.processor._validate_salary('150-200K') is False  # 高于最高限制
    
    def test_validate_url(self):
        """测试URL验证"""
        # 有效URL
        assert self.processor._validate_url('https://www.zhipin.com/job_detail/123.html') is True
        
        # 无效URL
        assert self.processor._validate_url('') is False
        assert self.processor._validate_url('invalid-url') is False
        assert self.processor._validate_url('https://other-site.com/job/123') is False
    
    def test_check_filters(self):
        """测试过滤条件"""
        # 包含排除关键词的数据
        job_with_excluded_keyword = {
            'job_title': '外包Python工程师',
            'job_description': '这是一个外包项目',
            'company_name': '正常公司',
            'company_info': '100-500人'
        }
        assert self.processor._check_filters(job_with_excluded_keyword) is False
        
        # 包含排除公司的数据
        job_with_excluded_company = {
            'job_title': 'Python工程师',
            'job_description': '正常工作',
            'company_name': '测试外包公司',
            'company_info': '100-500人'
        }
        assert self.processor._check_filters(job_with_excluded_company) is False
        
        # 正常数据
        normal_job = {
            'job_title': 'Python工程师',
            'job_description': '负责后端开发',
            'company_name': '正常科技公司',
            'company_info': '100-500人'
        }
        assert self.processor._check_filters(normal_job) is True
    
    def test_parse_salary(self):
        """测试薪资解析"""
        # 测试正常薪资格式
        result = self.processor._parse_salary('15-25K·14薪')
        assert result['salary_min'] == 15
        assert result['salary_max'] == 25
        assert result['salary_months'] == 14
        assert result['salary_unit'] == 'K'
        
        # 测试简单薪资格式
        result = self.processor._parse_salary('10-20K')
        assert result['salary_min'] == 10
        assert result['salary_max'] == 20
        assert result['salary_months'] == 12  # 默认12薪
        
        # 测试空薪资
        result = self.processor._parse_salary('')
        assert result['salary_min'] is None
        assert result['salary_max'] is None
    
    def test_parse_experience(self):
        """测试工作经验解析"""
        # 测试范围经验
        result = self.processor._parse_experience('3-5年')
        assert result['experience_min'] == 3
        assert result['experience_max'] == 5
        assert result['experience_type'] == '3-5年'
        
        # 测试单个年限
        result = self.processor._parse_experience('5年')
        assert result['experience_min'] == 5
        assert result['experience_max'] == 5
        
        # 测试无经验要求
        result = self.processor._parse_experience('不限')
        assert result['experience_min'] is None
        assert result['experience_max'] is None
    
    def test_parse_education(self):
        """测试学历解析"""
        # 测试各种学历
        result = self.processor._parse_education('本科')
        assert result['education_level'] == '本科'
        assert result['education_score'] == 5
        
        result = self.processor._parse_education('硕士')
        assert result['education_level'] == '硕士'
        assert result['education_score'] == 6
        
        result = self.processor._parse_education('大专')
        assert result['education_level'] == '大专'
        assert result['education_score'] == 4
        
        # 测试未知学历
        result = self.processor._parse_education('不限')
        assert result['education_score'] == 0
    
    def test_parse_company_size(self):
        """测试公司规模解析"""
        # 测试范围规模
        result = self.processor._parse_company_size('100-500人的公司')
        assert result['company_size_min'] == 100
        assert result['company_size_max'] == 500
        assert result['company_size_category'] == '中型'
        
        # 测试以上规模
        result = self.processor._parse_company_size('1000人以上')
        assert result['company_size_min'] == 1000
        assert result['company_size_max'] is None
        assert result['company_size_category'] == '大型'
        
        # 测试小型公司
        result = self.processor._parse_company_size('20-50人')
        assert result['company_size_category'] == '小型'
    
    def test_clean_data(self):
        """测试数据清洗"""
        dirty_data = {
            'job_title': '  Python工程师  \n\t',
            'job_description': '负责后端开发\n\n工作内容包括：\n1. 开发\n2. 测试',
            'company_name': 'ABC科技@#$%公司'
        }
        
        cleaned_data = self.processor._clean_data(dirty_data)
        
        assert cleaned_data['job_title'] == 'Python工程师'
        assert '负责后端开发 工作内容包括' in cleaned_data['job_description']
        assert 'ABC科技公司' in cleaned_data['company_name']
    
    def test_process_job_data(self):
        """测试完整数据处理"""
        raw_job_data = {
            'job_title': '  Python工程师  ',
            'salary_info': '15-25K·14薪',
            'benefits': '五险一金，带薪年假',
            'experience_req': '3-5年',
            'education_req': '本科',
            'job_description': '负责后端开发工作，要求熟悉Python',
            'company_info': '某科技公司，100-500人，A轮融资',
            'work_location': '北京市朝阳区',
            'job_url': 'https://www.zhipin.com/job_detail/123.html'
        }
        
        processed_data = self.processor.process_job_data(raw_job_data)
        
        # 检查数据清洗
        assert processed_data['job_title'] == 'Python工程师'
        
        # 检查结构化信息提取
        assert processed_data['salary_min'] == 15
        assert processed_data['salary_max'] == 25
        assert processed_data['salary_months'] == 14
        assert processed_data['experience_min'] == 3
        assert processed_data['experience_max'] == 5
        assert processed_data['education_score'] == 5
        assert processed_data['company_size_category'] == '中型'
        
        # 检查时间戳
        assert 'processed_time' in processed_data
    
    def test_get_processing_stats(self):
        """测试处理统计信息"""
        stats = self.processor.get_processing_stats()
        
        assert 'required_fields_count' in stats
        assert 'validation_enabled' in stats
        assert 'filters_enabled' in stats
        assert stats['required_fields_count'] == 9
        assert stats['validation_enabled'] is True
        assert stats['filters_enabled'] is True


if __name__ == '__main__':
    pytest.main([__file__])
