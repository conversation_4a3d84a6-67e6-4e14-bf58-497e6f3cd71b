# BOSS直聘爬虫系统 - Git忽略文件

# ==================== Python ====================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# 缓存文件
.cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ==================== 项目特定 ====================
# 日志文件
logs/
*.log

# 输出文件
output/
*.xlsx
*.csv
*.json

# 临时文件
temp/
tmp/
*.tmp

# 配置文件（包含敏感信息）
.env
config_local.yaml
secrets.yaml

# 浏览器相关
.playwright/
playwright-report/
test-results/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存文件
.cache/
cache/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# ==================== 操作系统 ====================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==================== 编辑器 ====================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==================== 其他工具 ====================
# Node.js (如果使用前端工具)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*



# ==================== 安全相关 ====================
# API密钥和敏感信息
*.key
*.pem
*.p12
*.pfx
secrets/
credentials/

# 证书文件
*.crt
*.cer
*.der

# ==================== 性能和调试 ====================
# 性能分析文件
*.prof
*.pstats

# 内存转储
*.dump
*.dmp

# 调试文件
debug/
*.debug

# ==================== 文档 ====================
# 自动生成的文档
docs/build/
docs/_build/
site/

# ==================== 特殊文件 ====================
# 锁文件
*.lock

# 进程ID文件
*.pid

# 套接字文件
*.sock

# 管道文件
*.fifo

# ==================== 自定义忽略 ====================
# 用户自定义的忽略文件
.gitignore.local

# 项目特定的临时文件
crawl_results_*
test_output_*
debug_*

# 大文件
*.large
files_over_100mb/

# 实验性代码
experimental/
sandbox/
playground/
