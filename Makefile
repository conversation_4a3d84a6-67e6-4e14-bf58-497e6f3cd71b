# BOSS直聘爬虫系统 - Makefile
# 提供常用的开发和部署命令

.PHONY: help install install-dev test test-cov lint format clean run run-test run-debug check docs build

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

# Python解释器
PYTHON := python3
PIP := pip3

# 项目信息
PROJECT_NAME := boss-crawler
VERSION := 1.0.0

help: ## 显示帮助信息
	@echo "$(BLUE)BOSS直聘爬虫系统 - 开发工具$(NC)"
	@echo ""
	@echo "$(GREEN)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## 安装生产环境依赖
	@echo "$(BLUE)安装生产环境依赖...$(NC)"
	$(PIP) install -r requirements.txt
	playwright install chromium
	@echo "$(GREEN)生产环境依赖安装完成$(NC)"

install-dev: ## 安装开发环境依赖
	@echo "$(BLUE)安装开发环境依赖...$(NC)"
	$(PIP) install -r requirements-dev.txt
	playwright install chromium
	pre-commit install
	@echo "$(GREEN)开发环境依赖安装完成$(NC)"

test: ## 运行测试
	@echo "$(BLUE)运行测试...$(NC)"
	pytest tests/ -v

test-cov: ## 运行测试并生成覆盖率报告
	@echo "$(BLUE)运行测试并生成覆盖率报告...$(NC)"
	pytest tests/ -v --cov=src --cov-report=html --cov-report=term

test-unit: ## 运行单元测试
	@echo "$(BLUE)运行单元测试...$(NC)"
	pytest tests/ -v -m "unit"

test-integration: ## 运行集成测试
	@echo "$(BLUE)运行集成测试...$(NC)"
	pytest tests/ -v -m "integration"

lint: ## 代码检查
	@echo "$(BLUE)运行代码检查...$(NC)"
	flake8 src/ tests/
	pylint src/
	mypy src/

format: ## 代码格式化
	@echo "$(BLUE)格式化代码...$(NC)"
	black src/ tests/
	isort src/ tests/
	@echo "$(GREEN)代码格式化完成$(NC)"

format-check: ## 检查代码格式
	@echo "$(BLUE)检查代码格式...$(NC)"
	black --check src/ tests/
	isort --check-only src/ tests/

security: ## 安全检查
	@echo "$(BLUE)运行安全检查...$(NC)"
	bandit -r src/
	safety check

clean: ## 清理临时文件
	@echo "$(BLUE)清理临时文件...$(NC)"
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .mypy_cache/
	rm -rf logs/*.log
	rm -rf output/*.xlsx
	rm -rf temp/*
	@echo "$(GREEN)清理完成$(NC)"

run: ## 运行爬虫（正常模式）
	@echo "$(BLUE)启动BOSS直聘爬虫...$(NC)"
	$(PYTHON) run.py

run-test: ## 运行爬虫（测试模式）
	@echo "$(BLUE)启动BOSS直聘爬虫（测试模式）...$(NC)"
	$(PYTHON) run.py --test

run-debug: ## 运行爬虫（调试模式）
	@echo "$(BLUE)启动BOSS直聘爬虫（调试模式）...$(NC)"
	$(PYTHON) run.py --debug

run-headless: ## 运行爬虫（无头模式）
	@echo "$(BLUE)启动BOSS直聘爬虫（无头模式）...$(NC)"
	$(PYTHON) run.py --headless

check: ## 环境检查
	@echo "$(BLUE)运行环境检查...$(NC)"
	$(PYTHON) run.py --check

setup: ## 初始化项目环境
	@echo "$(BLUE)初始化项目环境...$(NC)"
	$(PYTHON) install.py
	@echo "$(GREEN)项目环境初始化完成$(NC)"

docs: ## 生成文档
	@echo "$(BLUE)生成文档...$(NC)"
	@if [ -d "docs" ]; then \
		cd docs && make html; \
	else \
		echo "$(YELLOW)docs目录不存在，跳过文档生成$(NC)"; \
	fi

build: ## 构建项目
	@echo "$(BLUE)构建项目...$(NC)"
	$(PYTHON) -m build
	@echo "$(GREEN)项目构建完成$(NC)"

package: ## 打包项目
	@echo "$(BLUE)打包项目...$(NC)"
	$(PYTHON) setup.py sdist bdist_wheel
	@echo "$(GREEN)项目打包完成$(NC)"

install-local: ## 本地安装项目
	@echo "$(BLUE)本地安装项目...$(NC)"
	$(PIP) install -e .
	@echo "$(GREEN)本地安装完成$(NC)"

upgrade: ## 升级依赖包
	@echo "$(BLUE)升级依赖包...$(NC)"
	$(PIP) install --upgrade -r requirements.txt
	@echo "$(GREEN)依赖包升级完成$(NC)"

freeze: ## 冻结当前环境依赖
	@echo "$(BLUE)冻结当前环境依赖...$(NC)"
	$(PIP) freeze > requirements-freeze.txt
	@echo "$(GREEN)依赖已冻结到 requirements-freeze.txt$(NC)"

venv: ## 创建虚拟环境
	@echo "$(BLUE)创建虚拟环境...$(NC)"
	$(PYTHON) -m venv venv
	@echo "$(GREEN)虚拟环境创建完成$(NC)"
	@echo "$(YELLOW)请运行: source venv/bin/activate (Linux/macOS) 或 venv\\Scripts\\activate (Windows)$(NC)"

docker-build: ## 构建Docker镜像
	@echo "$(BLUE)构建Docker镜像...$(NC)"
	docker build -t $(PROJECT_NAME):$(VERSION) .
	@echo "$(GREEN)Docker镜像构建完成$(NC)"

docker-run: ## 运行Docker容器
	@echo "$(BLUE)运行Docker容器...$(NC)"
	docker run --rm -it -v $(PWD)/output:/app/output $(PROJECT_NAME):$(VERSION)

pre-commit: ## 运行pre-commit检查
	@echo "$(BLUE)运行pre-commit检查...$(NC)"
	pre-commit run --all-files

ci: format-check lint test security ## 运行CI检查流程
	@echo "$(GREEN)CI检查流程完成$(NC)"

all: clean install-dev format lint test docs build ## 完整的开发流程
	@echo "$(GREEN)完整开发流程完成$(NC)"

# 快速命令别名
t: test ## 测试的快捷方式
f: format ## 格式化的快捷方式
l: lint ## 检查的快捷方式
r: run ## 运行的快捷方式
c: clean ## 清理的快捷方式

# 显示项目信息
info: ## 显示项目信息
	@echo "$(BLUE)项目信息:$(NC)"
	@echo "  名称: $(PROJECT_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  Python: $(shell $(PYTHON) --version)"
	@echo "  工作目录: $(PWD)"
	@echo "  虚拟环境: $(VIRTUAL_ENV)"

# 性能测试
benchmark: ## 运行性能测试
	@echo "$(BLUE)运行性能测试...$(NC)"
	$(PYTHON) -m pytest tests/ -v -m "slow" --benchmark-only

# 内存分析
profile: ## 运行内存分析
	@echo "$(BLUE)运行内存分析...$(NC)"
	$(PYTHON) -m memory_profiler run.py --test

# 生成需求文件
requirements: ## 生成requirements.txt
	@echo "$(BLUE)生成requirements.txt...$(NC)"
	pipreqs . --force
	@echo "$(GREEN)requirements.txt已更新$(NC)"
