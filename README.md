# BOSS直聘全站爬虫系统

🚀 **基于GitHub最佳实践和AI技术的全面爬虫解决方案**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![AI Powered](https://img.shields.io/badge/AI-Powered-orange.svg)](https://openai.com/)

## 📋 项目简介

这是一个专为BOSS直聘设计的全站数据爬虫系统，集成了最新的反爬虫技术、AI智能分析和实时数据保存功能。系统基于GitHub上的最佳实践开发，确保高效、稳定、可靠的数据采集。

### 🎯 核心特性

- **🛡️ 智能反爬虫**：集成代理轮换、验证码自动处理、设备指纹伪造
- **🤖 AI技术驱动**：智能岗位匹配、质量评分、薪资分析
- **💾 实时数据保存**：防止数据丢失，支持Excel格式输出
- **🌐 全国覆盖**：支持全国所有城市的岗位数据爬取
- **📊 专业数据处理**：9个必须字段的完整提取和验证
- **⚡ 高性能架构**：异步处理、并发控制、内存优化

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Google Chrome 浏览器
- 2GB+ 可用内存
- 1GB+ 磁盘空间

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/boss-crawler.git
cd boss-crawler
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **安装Playwright浏览器**
```bash
playwright install chromium
```

4. **配置环境变量**
```bash
cp .env.template .env
# 编辑 .env 文件，填入必要的配置
```

5. **运行系统**
```bash
python main.py
```

## ⚙️ 配置说明

### 基础配置 (config.yaml)

```yaml
# 搜索参数配置
boss:
  search_params:
    city_codes: ["*********", "*********"]  # 北京、上海
    keywords: ["Python工程师", "Java工程师"]
    salary_range: [10, 50]  # 10K-50K
    
# 代理配置
proxy:
  enabled: true
  http_proxy: "http://127.0.0.1:7890"
  
# AI配置
ai:
  enabled: true
  openai:
    api_key: "${OPENAI_API_KEY}"
    model: "gpt-4o-mini"
```

### 环境变量配置 (.env)

```bash
# AI配置
OPENAI_API_KEY=sk-your-api-key
OPENAI_BASE_URL=https://api.openai.com/v1

# 通知配置
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx

# 代理配置
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890
```

## 📊 数据字段

系统会提取以下9个必须字段：

| 字段名 | 描述 | 示例 |
|--------|------|------|
| job_title | 岗位名称 | "Python后端工程师" |
| salary_info | 薪资情况 | "15-25K·14薪" |
| benefits | 福利待遇 | "五险一金、带薪年假、弹性工作" |
| experience_req | 工作经验要求 | "3-5年" |
| education_req | 学历要求 | "本科" |
| job_description | 职位详细要求 | "负责后端系统开发..." |
| company_info | 公司介绍 | "某某科技有限公司..." |
| work_location | 工作地址 | "北京市朝阳区..." |
| job_url | 岗位详情页URL | "https://www.zhipin.com/job_detail/xxx.html" |

## 🛡️ 反爬虫技术

### 核心技术栈

- **浏览器指纹伪造**：移除webdriver属性，伪造设备信息
- **代理轮换管理**：智能代理健康检查和故障转移
- **验证码自动处理**：基于ddddocr的滑块、点击、文字验证码识别
- **请求随机化**：随机延迟、UA轮换、请求头伪造
- **环境检测绕过**：canvas、webgl、audio指纹处理

### 技术实现

```python
# 反检测脚本注入
await self.context.add_init_script("""
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
    });
    
    window.chrome = {
        runtime: {},
        loadTimes: function() {},
        csi: function() {},
        app: {}
    };
""")
```

## 🤖 AI功能

### 智能分析能力

- **岗位质量评分**：基于描述完整性、薪资透明度等维度
- **技能匹配度**：自动提取技能关键词并计算匹配度
- **薪资竞争力分析**：根据行业和级别评估薪资水平
- **公司评级**：分析公司规模、类型和发展前景

### AI配置

支持多种AI服务：

- **OpenAI GPT-4o-mini**：官方API
- **如云API**：国内代理服务，2人民币=1美元
- **本地模型**：支持本地部署的大语言模型

## 📈 性能优化

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   爬虫管理器     │────│   反爬虫模块     │────│   数据处理器     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI分析器      │────│   Excel导出器    │────│   进度跟踪器     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 性能特性

- **异步并发**：最大3个并发任务，避免触发反爬虫
- **内存管理**：智能垃圾回收，最大内存使用1GB
- **实时保存**：每50条数据自动保存，防止数据丢失
- **进度跟踪**：实时显示爬取进度和成功率

## 📁 项目结构

```
Position_url_crawler/
├── main.py                 # 主程序入口
├── run.py                  # 快速启动脚本
├── install.py              # 自动安装脚本
├── config.yaml            # 配置文件
├── requirements.txt        # 依赖包
├── .env.template          # 环境变量模板
├── README.md              # 项目文档
├── LICENSE                # 许可证
├── CHANGELOG.md           # 更新日志
├── src/
│   ├── core/              # 核心模块
│   │   ├── config_manager.py
│   │   └── crawler_manager.py
│   ├── crawlers/          # 爬虫模块
│   │   └── boss_crawler.py
│   ├── anti_crawler/      # 反爬虫模块
│   │   ├── proxy_manager.py
│   │   ├── captcha_solver.py
│   │   └── user_agent_manager.py
│   ├── data/              # 数据处理
│   │   ├── data_processor.py
│   │   └── excel_exporter.py
│   ├── ai/                # AI模块
│   │   └── job_analyzer.py
│   └── utils/             # 工具模块
│       ├── logger_setup.py
│       ├── environment_check.py
│       ├── url_validator.py
│       └── progress_tracker.py
├── scripts/               # 启动脚本
│   ├── start.sh           # Linux/macOS启动脚本
│   └── start.bat          # Windows启动脚本
├── logs/                  # 日志目录
└── output/               # 输出目录
```

## 🔧 高级配置

### 自定义城市代码

```yaml
boss:
  search_params:
    city_codes:
      - "*********"  # 北京
      - "*********"  # 上海
      - "*********"  # 广州
      - "*********"  # 深圳
      - "*********"  # 成都
```

### 过滤条件设置

```yaml
data_extraction:
  filters:
    exclude_keywords: ["外包", "外派", "实习"]
    exclude_companies: ["某某外包公司"]
    min_company_size: 50
```

### 代理池配置

```yaml
proxy:
  enabled: true
  proxy_pool:
    - "http://proxy1:port"
    - "http://proxy2:port"
    - "http://proxy3:port"
```

## 📊 监控和日志

### 日志系统

- **分级日志**：DEBUG、INFO、WARNING、ERROR
- **文件轮转**：按日期自动轮转，压缩存储
- **实时监控**：控制台彩色输出，文件详细记录

### 进度监控

```
📊 进度: 150/500 (30.0%) | 成功: 145 | 失败: 5 | 成功率: 96.7%
```

### 企业微信通知

支持企业微信机器人推送爬取结果：

```json
{
  "msgtype": "text",
  "text": {
    "content": "🎉 BOSS直聘爬取完成！\n总计岗位: 500\n有效岗位: 485\n成功率: 97.0%"
  }
}
```

## 🚨 注意事项

### 使用规范

1. **遵守法律法规**：仅用于学习和研究目的
2. **尊重网站规则**：合理控制爬取频率
3. **数据使用规范**：不得用于商业用途
4. **隐私保护**：妥善处理个人信息

### 技术限制

- **IP限制**：建议使用代理服务
- **频率限制**：默认2-5秒间隔
- **验证码**：复杂验证码需手动处理
- **数据量**：单次建议不超过1000个岗位

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境

```bash
# 安装依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium

# 运行程序
python run.py
```

### 提交规范

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式
- refactor: 重构

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [xishandong/crawlProject](https://github.com/xishandong/crawlProject) - 爬虫技术参考
- [loks666/get_jobs](https://github.com/loks666/get_jobs) - AI找工作助手
- [Playwright](https://playwright.dev/) - 浏览器自动化
- [ddddocr](https://github.com/sml2h3/ddddocr) - 验证码识别

## 📞 联系方式

- 项目地址：[GitHub Repository](https://github.com/your-username/boss-crawler)
- 问题反馈：[Issues](https://github.com/your-username/boss-crawler/issues)
- 邮箱：<EMAIL>

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！
