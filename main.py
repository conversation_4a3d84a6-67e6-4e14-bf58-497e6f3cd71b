#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘全站爬虫系统 - 主程序
基于GitHub最佳实践和AI技术的全面爬虫解决方案

作者: AI Assistant
版本: 1.0.0
日期: 2025-07-03
"""

import asyncio
import sys
import os
from pathlib import Path
from loguru import logger
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.crawler_manager import CrawlerManager
from src.core.config_manager import ConfigManager
from src.utils.logger_setup import setup_logger
from src.utils.environment_check import check_environment


class BossCrawlerApp:
    """BOSS直聘爬虫应用主类"""
    
    def __init__(self):
        """初始化应用"""
        self.config_manager = ConfigManager()
        self.crawler_manager = None
        self.start_time = datetime.now()
        
    async def initialize(self):
        """异步初始化"""
        try:
            # 设置日志
            setup_logger(self.config_manager.config)
            
            # 环境检查
            logger.info("🔍 开始环境检查...")
            if not check_environment():
                logger.error("❌ 环境检查失败，请检查依赖和配置")
                return False
                
            # 初始化爬虫管理器
            logger.info("🚀 初始化爬虫管理器...")
            self.crawler_manager = CrawlerManager(self.config_manager)
            await self.crawler_manager.initialize()
            
            logger.success("✅ 系统初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            return False
    
    async def run(self):
        """运行爬虫"""
        try:
            if not await self.initialize():
                return False
                
            logger.info("🎯 开始BOSS直聘全站数据爬取...")
            logger.info("=" * 60)
            
            # 显示配置信息
            self._show_config_info()
            
            # 开始爬取
            results = await self.crawler_manager.start_crawling()
            
            # 显示结果统计
            self._show_results(results)
            
            return True
            
        except KeyboardInterrupt:
            logger.warning("⚠️ 用户中断爬取")
            return False
        except Exception as e:
            logger.error(f"❌ 爬取过程中发生错误: {e}")
            return False
        finally:
            await self.cleanup()
    
    def _show_config_info(self):
        """显示配置信息"""
        config = self.config_manager.config
        
        logger.info("📋 当前配置信息:")
        logger.info(f"   🏙️  目标城市: {config['boss']['search_params']['city_codes']}")
        logger.info(f"   🔍 搜索关键词: {config['boss']['search_params']['keywords']}")
        logger.info(f"   💰 薪资范围: {config['boss']['search_params']['salary_range']}K")
        logger.info(f"   🌐 代理状态: {'启用' if config['proxy']['enabled'] else '禁用'}")
        logger.info(f"   🤖 AI功能: {'启用' if config['ai']['enabled'] else '禁用'}")
        logger.info("=" * 60)
    
    def _show_results(self, results):
        """显示结果统计"""
        if not results:
            logger.warning("⚠️ 未获取到任何数据")
            return
            
        total_jobs = results.get('total_jobs', 0)
        valid_jobs = results.get('valid_jobs', 0)
        saved_jobs = results.get('saved_jobs', 0)
        
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        logger.info("=" * 60)
        logger.success("🎉 爬取完成！统计信息:")
        logger.info(f"   📊 总计岗位: {total_jobs}")
        logger.info(f"   ✅ 有效岗位: {valid_jobs}")
        logger.info(f"   💾 已保存岗位: {saved_jobs}")
        logger.info(f"   ⏱️  总耗时: {duration}")
        logger.info(f"   📁 输出文件: {results.get('output_file', 'N/A')}")
        logger.info("=" * 60)
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.crawler_manager:
                await self.crawler_manager.cleanup()
            logger.info("🧹 资源清理完成")
        except Exception as e:
            logger.error(f"❌ 清理资源时发生错误: {e}")


async def main():
    """主函数"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    BOSS直聘全站爬虫系统                        ║
    ║                                                              ║
    ║  🚀 基于GitHub最佳实践和AI技术的全面爬虫解决方案                ║
    ║  🎯 支持全国所有城市，智能反爬虫，实时数据保存                   ║
    ║  💡 集成本地代理，AI智能匹配，Excel格式输出                     ║
    ║                                                              ║
    ║  版本: 1.0.0  |  作者: AI Assistant  |  日期: 2025-07-03      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    app = BossCrawlerApp()
    success = await app.run()
    
    if success:
        print("\n🎉 爬取任务完成！请查看输出文件。")
        return 0
    else:
        print("\n❌ 爬取任务失败！请查看日志了解详情。")
        return 1


if __name__ == "__main__":
    try:
        # 设置事件循环策略（Windows兼容性）
        if sys.platform.startswith('win'):
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行主程序
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        sys.exit(1)
