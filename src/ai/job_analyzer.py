#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI岗位分析器 - 使用AI技术分析岗位信息
基于OpenAI API和本地AI模型
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional
from loguru import logger
import openai
from openai import AsyncOpenAI
import aiohttp


class JobAnalyzer:
    """AI岗位分析器类"""
    
    def __init__(self, ai_config: Dict[str, Any]):
        """
        初始化AI岗位分析器
        
        Args:
            ai_config: AI配置
        """
        self.config = ai_config
        self.openai_config = ai_config.get('openai', {})
        self.smart_matching_config = ai_config.get('smart_matching', {})
        self.auto_analysis_config = ai_config.get('auto_analysis', {})
        
        self.client = None
        self.is_initialized = False
        
        # 分析缓存
        self.analysis_cache = {}
        
        # 技能关键词库
        self.skill_keywords = self._load_skill_keywords()
        
        # 行业关键词库
        self.industry_keywords = self._load_industry_keywords()
    
    async def initialize(self):
        """初始化AI分析器"""
        try:
            logger.info("🔧 初始化AI岗位分析器...")
            
            # 初始化OpenAI客户端
            if self.openai_config.get('api_key'):
                self.client = AsyncOpenAI(
                    api_key=self.openai_config['api_key'],
                    base_url=self.openai_config.get('base_url', 'https://api.openai.com/v1')
                )
                
                # 测试API连接
                await self._test_api_connection()
                logger.info("✅ OpenAI API连接成功")
            else:
                logger.warning("⚠️ 未配置OpenAI API密钥，将使用本地分析")
            
            self.is_initialized = True
            logger.success("✅ AI岗位分析器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ AI岗位分析器初始化失败: {e}")
            # 不抛出异常，允许系统在没有AI的情况下运行
            self.is_initialized = False
    
    async def _test_api_connection(self):
        """测试API连接"""
        try:
            response = await self.client.chat.completions.create(
                model=self.openai_config.get('model', 'gpt-4o-mini'),
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            
            if response.choices:
                logger.debug("API连接测试成功")
            
        except Exception as e:
            logger.error(f"❌ API连接测试失败: {e}")
            raise
    
    def _load_skill_keywords(self) -> Dict[str, List[str]]:
        """加载技能关键词库"""
        return {
            'programming_languages': [
                'Python', 'Java', 'JavaScript', 'TypeScript', 'Go', 'Golang',
                'C++', 'C#', 'PHP', 'Ruby', 'Swift', 'Kotlin', 'Rust', 'Scala'
            ],
            'frameworks': [
                'Spring', 'Django', 'Flask', 'React', 'Vue', 'Angular',
                'Express', 'FastAPI', 'Laravel', 'Rails', 'ASP.NET'
            ],
            'databases': [
                'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Oracle',
                'SQL Server', 'SQLite', 'Elasticsearch', 'ClickHouse'
            ],
            'cloud_platforms': [
                'AWS', 'Azure', 'Google Cloud', 'Alibaba Cloud', '阿里云',
                'Tencent Cloud', '腾讯云', 'Kubernetes', 'Docker'
            ],
            'ai_ml': [
                'Machine Learning', 'Deep Learning', 'TensorFlow', 'PyTorch',
                'Scikit-learn', 'NLP', 'Computer Vision', 'LLM', '大模型'
            ],
            'tools': [
                'Git', 'Jenkins', 'Docker', 'Kubernetes', 'Nginx',
                'Apache', 'Kafka', 'RabbitMQ', 'Prometheus', 'Grafana'
            ]
        }
    
    def _load_industry_keywords(self) -> Dict[str, List[str]]:
        """加载行业关键词库"""
        return {
            'internet': ['互联网', '电商', '社交', '搜索', '在线教育', '直播'],
            'fintech': ['金融科技', '支付', '区块链', '数字货币', '保险科技'],
            'ai': ['人工智能', 'AI', '机器学习', '深度学习', '自然语言处理'],
            'enterprise': ['企业服务', 'SaaS', 'ERP', 'CRM', 'OA系统'],
            'gaming': ['游戏', '手游', '网游', '游戏引擎', 'Unity'],
            'healthcare': ['医疗', '健康', '生物技术', '医药', '医疗器械'],
            'automotive': ['汽车', '新能源', '自动驾驶', '车联网', '智能汽车'],
            'ecommerce': ['电商', '零售', '供应链', '物流', '新零售']
        }
    
    async def analyze_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析岗位信息
        
        Args:
            job_data: 岗位数据
            
        Returns:
            分析结果
        """
        try:
            if not self.is_initialized:
                return await self._local_analysis(job_data)
            
            analysis_result = {}
            
            # 智能匹配分析
            if self.smart_matching_config.get('enabled', True):
                matching_result = await self._smart_matching_analysis(job_data)
                analysis_result.update(matching_result)
            
            # 自动分析
            if self.auto_analysis_config.get('job_quality_score', True):
                quality_score = await self._analyze_job_quality(job_data)
                analysis_result['job_quality_score'] = quality_score
            
            if self.auto_analysis_config.get('salary_analysis', True):
                salary_analysis = await self._analyze_salary(job_data)
                analysis_result.update(salary_analysis)
            
            if self.auto_analysis_config.get('company_rating', True):
                company_rating = await self._analyze_company(job_data)
                analysis_result.update(company_rating)
            
            # AI深度分析（如果启用）
            if self.client:
                ai_analysis = await self._ai_deep_analysis(job_data)
                analysis_result.update(ai_analysis)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ 分析岗位失败: {e}")
            return await self._local_analysis(job_data)
    
    async def _local_analysis(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        本地分析（不依赖AI API）
        
        Args:
            job_data: 岗位数据
            
        Returns:
            分析结果
        """
        try:
            result = {}
            
            # 技能提取
            skills = self._extract_skills(job_data.get('job_description', ''))
            result['extracted_skills'] = skills
            
            # 行业识别
            industry = self._identify_industry(job_data)
            result['identified_industry'] = industry
            
            # 岗位级别判断
            job_level = self._determine_job_level(job_data)
            result['job_level'] = job_level
            
            # 薪资竞争力
            salary_competitiveness = self._analyze_salary_competitiveness(job_data)
            result['salary_competitiveness'] = salary_competitiveness
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 本地分析失败: {e}")
            return {}
    
    def _extract_skills(self, job_description: str) -> List[str]:
        """
        提取技能关键词
        
        Args:
            job_description: 岗位描述
            
        Returns:
            技能列表
        """
        try:
            extracted_skills = []
            job_desc_lower = job_description.lower()
            
            for category, skills in self.skill_keywords.items():
                for skill in skills:
                    if skill.lower() in job_desc_lower:
                        extracted_skills.append(skill)
            
            return list(set(extracted_skills))
            
        except Exception as e:
            logger.error(f"❌ 提取技能失败: {e}")
            return []
    
    def _identify_industry(self, job_data: Dict[str, Any]) -> str:
        """
        识别行业
        
        Args:
            job_data: 岗位数据
            
        Returns:
            行业类型
        """
        try:
            company_info = job_data.get('company_info', '').lower()
            job_title = job_data.get('job_title', '').lower()
            job_desc = job_data.get('job_description', '').lower()
            
            text_to_analyze = f"{company_info} {job_title} {job_desc}"
            
            industry_scores = {}
            
            for industry, keywords in self.industry_keywords.items():
                score = 0
                for keyword in keywords:
                    if keyword.lower() in text_to_analyze:
                        score += 1
                industry_scores[industry] = score
            
            if industry_scores:
                best_industry = max(industry_scores, key=industry_scores.get)
                if industry_scores[best_industry] > 0:
                    return best_industry
            
            return 'unknown'
            
        except Exception as e:
            logger.error(f"❌ 识别行业失败: {e}")
            return 'unknown'
    
    def _determine_job_level(self, job_data: Dict[str, Any]) -> str:
        """
        判断岗位级别
        
        Args:
            job_data: 岗位数据
            
        Returns:
            岗位级别
        """
        try:
            job_title = job_data.get('job_title', '').lower()
            experience_req = job_data.get('experience_req', '').lower()
            salary_min = job_data.get('salary_min', 0)
            
            # 根据职位名称判断
            if any(keyword in job_title for keyword in ['总监', 'director', '总经理', 'vp', 'cto', 'ceo']):
                return 'executive'
            elif any(keyword in job_title for keyword in ['经理', 'manager', '主管', 'lead', '负责人']):
                return 'manager'
            elif any(keyword in job_title for keyword in ['高级', 'senior', '资深', '专家']):
                return 'senior'
            elif any(keyword in job_title for keyword in ['初级', 'junior', '助理', '实习']):
                return 'junior'
            
            # 根据经验要求判断
            if '5年以上' in experience_req or '10年' in experience_req:
                return 'senior'
            elif '3-5年' in experience_req:
                return 'mid'
            elif '1-3年' in experience_req:
                return 'junior'
            
            # 根据薪资判断
            if salary_min >= 50:
                return 'senior'
            elif salary_min >= 25:
                return 'mid'
            elif salary_min >= 10:
                return 'junior'
            
            return 'mid'
            
        except Exception as e:
            logger.error(f"❌ 判断岗位级别失败: {e}")
            return 'unknown'
    
    def _analyze_salary_competitiveness(self, job_data: Dict[str, Any]) -> str:
        """
        分析薪资竞争力
        
        Args:
            job_data: 岗位数据
            
        Returns:
            竞争力等级
        """
        try:
            salary_min = job_data.get('salary_min', 0)
            salary_max = job_data.get('salary_max', 0)
            job_level = self._determine_job_level(job_data)
            
            if not salary_min:
                return 'unknown'
            
            # 根据岗位级别设定基准
            benchmarks = {
                'junior': {'low': 8, 'mid': 15, 'high': 25},
                'mid': {'low': 15, 'mid': 25, 'high': 40},
                'senior': {'low': 25, 'mid': 40, 'high': 60},
                'manager': {'low': 30, 'mid': 50, 'high': 80},
                'executive': {'low': 50, 'mid': 80, 'high': 120}
            }
            
            benchmark = benchmarks.get(job_level, benchmarks['mid'])
            
            if salary_min >= benchmark['high']:
                return 'high'
            elif salary_min >= benchmark['mid']:
                return 'medium'
            elif salary_min >= benchmark['low']:
                return 'low'
            else:
                return 'very_low'
                
        except Exception as e:
            logger.error(f"❌ 分析薪资竞争力失败: {e}")
            return 'unknown'
    
    async def _smart_matching_analysis(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        智能匹配分析
        
        Args:
            job_data: 岗位数据
            
        Returns:
            匹配分析结果
        """
        try:
            result = {}
            
            # 技能匹配度
            skills = self._extract_skills(job_data.get('job_description', ''))
            result['skill_match_count'] = len(skills)
            
            # 匹配度评分
            threshold = self.smart_matching_config.get('threshold', 0.7)
            match_score = min(len(skills) / 10.0, 1.0)  # 简单评分逻辑
            result['match_score'] = match_score
            result['is_good_match'] = match_score >= threshold
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 智能匹配分析失败: {e}")
            return {}
    
    async def _analyze_job_quality(self, job_data: Dict[str, Any]) -> float:
        """
        分析岗位质量评分
        
        Args:
            job_data: 岗位数据
            
        Returns:
            质量评分 (0-1)
        """
        try:
            score = 0.0
            
            # 岗位描述完整性 (0.3)
            job_desc = job_data.get('job_description', '')
            if len(job_desc) > 100:
                score += 0.1
            if len(job_desc) > 500:
                score += 0.1
            if len(job_desc) > 1000:
                score += 0.1
            
            # 薪资透明度 (0.2)
            if job_data.get('salary_min') and job_data.get('salary_max'):
                score += 0.2
            
            # 公司信息完整性 (0.2)
            company_info = job_data.get('company_info', '')
            if len(company_info) > 50:
                score += 0.1
            if len(company_info) > 200:
                score += 0.1
            
            # 福利待遇 (0.2)
            benefits = job_data.get('benefits', '')
            if benefits:
                score += 0.1
            if len(benefits) > 50:
                score += 0.1
            
            # 要求合理性 (0.1)
            experience_req = job_data.get('experience_req', '')
            education_req = job_data.get('education_req', '')
            if experience_req and education_req:
                score += 0.1
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"❌ 分析岗位质量失败: {e}")
            return 0.0
    
    async def _analyze_salary(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析薪资信息
        
        Args:
            job_data: 岗位数据
            
        Returns:
            薪资分析结果
        """
        try:
            result = {}
            
            salary_min = job_data.get('salary_min', 0)
            salary_max = job_data.get('salary_max', 0)
            salary_months = job_data.get('salary_months', 12)
            
            if salary_min and salary_max:
                # 年薪计算
                annual_min = salary_min * salary_months
                annual_max = salary_max * salary_months
                
                result['annual_salary_min'] = annual_min
                result['annual_salary_max'] = annual_max
                result['salary_range_width'] = salary_max - salary_min
                result['salary_transparency'] = 'high' if salary_max > salary_min else 'medium'
            else:
                result['salary_transparency'] = 'low'
            
            # 薪资竞争力
            result['salary_competitiveness'] = self._analyze_salary_competitiveness(job_data)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 分析薪资失败: {e}")
            return {}
    
    async def _analyze_company(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析公司信息
        
        Args:
            job_data: 岗位数据
            
        Returns:
            公司分析结果
        """
        try:
            result = {}
            
            company_info = job_data.get('company_info', '')
            company_name = job_data.get('company_name', '')
            
            # 公司规模评估
            company_size_category = job_data.get('company_size_category', '')
            if company_size_category:
                result['company_size_rating'] = {
                    '大型': 'excellent',
                    '中型': 'good',
                    '小型': 'fair'
                }.get(company_size_category, 'unknown')
            
            # 公司信息完整性
            info_completeness = 'high' if len(company_info) > 200 else 'medium' if len(company_info) > 50 else 'low'
            result['company_info_completeness'] = info_completeness
            
            # 公司类型识别
            if any(keyword in company_info.lower() for keyword in ['上市', '股份', '集团']):
                result['company_type'] = 'public'
            elif any(keyword in company_info.lower() for keyword in ['创业', '初创', 'startup']):
                result['company_type'] = 'startup'
            else:
                result['company_type'] = 'private'
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 分析公司失败: {e}")
            return {}
    
    async def _ai_deep_analysis(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI深度分析
        
        Args:
            job_data: 岗位数据
            
        Returns:
            AI分析结果
        """
        try:
            if not self.client:
                return {}
            
            # 构建分析提示
            prompt = self._build_analysis_prompt(job_data)
            
            # 调用AI API
            response = await self.client.chat.completions.create(
                model=self.openai_config.get('model', 'gpt-4o-mini'),
                messages=[
                    {"role": "system", "content": "你是一个专业的HR和职业分析师，请分析以下岗位信息。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            if response.choices:
                ai_analysis = response.choices[0].message.content
                return self._parse_ai_analysis(ai_analysis)
            
            return {}
            
        except Exception as e:
            logger.error(f"❌ AI深度分析失败: {e}")
            return {}
    
    def _build_analysis_prompt(self, job_data: Dict[str, Any]) -> str:
        """
        构建AI分析提示
        
        Args:
            job_data: 岗位数据
            
        Returns:
            分析提示
        """
        prompt = f"""
        请分析以下岗位信息，并以JSON格式返回分析结果：

        岗位名称：{job_data.get('job_title', '')}
        薪资情况：{job_data.get('salary_info', '')}
        工作经验：{job_data.get('experience_req', '')}
        学历要求：{job_data.get('education_req', '')}
        公司信息：{job_data.get('company_info', '')[:200]}
        岗位描述：{job_data.get('job_description', '')[:500]}

        请分析：
        1. 岗位发展前景 (career_prospect): excellent/good/fair/poor
        2. 技能要求难度 (skill_difficulty): high/medium/low
        3. 工作压力预估 (work_pressure): high/medium/low
        4. 推荐指数 (recommendation): 1-10分
        5. 关键优势 (key_advantages): 列表形式
        6. 潜在风险 (potential_risks): 列表形式

        返回JSON格式，不要其他文字。
        """
        
        return prompt
    
    def _parse_ai_analysis(self, ai_response: str) -> Dict[str, Any]:
        """
        解析AI分析结果
        
        Args:
            ai_response: AI响应
            
        Returns:
            解析后的结果
        """
        try:
            # 尝试解析JSON
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            
            return {'ai_analysis_raw': ai_response}
            
        except Exception as e:
            logger.error(f"❌ 解析AI分析结果失败: {e}")
            return {'ai_analysis_raw': ai_response}
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 清理AI分析器资源...")
            
            if self.client:
                await self.client.close()
            
            self.analysis_cache.clear()
            self.is_initialized = False
            
            logger.info("✅ AI分析器资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理AI分析器资源失败: {e}")
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """
        获取分析统计信息
        
        Returns:
            统计信息
        """
        return {
            'is_initialized': self.is_initialized,
            'has_openai_client': bool(self.client),
            'cache_size': len(self.analysis_cache),
            'skill_categories': len(self.skill_keywords),
            'industry_categories': len(self.industry_keywords),
            'smart_matching_enabled': self.smart_matching_config.get('enabled', False),
            'auto_analysis_enabled': any(self.auto_analysis_config.values())
        }
