#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BOSS直聘爬虫 - 核心爬虫实现
基于GitHub最佳实践，集成反爬虫技术
"""

import asyncio
import json
import re
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse, parse_qs
from loguru import logger
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from bs4 import BeautifulSoup
import random
import time

from ..anti_crawler.user_agent_manager import UserAgentManager
from ..utils.url_validator import URLValidator


class BossCrawler:
    """BOSS直聘爬虫类"""
    
    def __init__(self, config: Dict[str, Any], proxy_manager=None, 
                 captcha_solver=None, data_processor=None):
        """
        初始化BOSS爬虫
        
        Args:
            config: 配置字典
            proxy_manager: 代理管理器
            captcha_solver: 验证码解决器
            data_processor: 数据处理器
        """
        self.config = config
        self.boss_config = config['boss']
        self.anti_crawler_config = config['anti_crawler']
        
        self.proxy_manager = proxy_manager
        self.captcha_solver = captcha_solver
        self.data_processor = data_processor
        
        # 浏览器相关
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        # 工具类
        self.ua_manager = UserAgentManager()
        self.url_validator = URLValidator()
        
        # 状态管理
        self.is_initialized = False
        self.login_status = False
        
        # 城市映射
        self.city_mapping = {
            "101010100": "北京",
            "101020100": "上海", 
            "101280100": "广州",
            "101280600": "深圳",
            "101270100": "成都",
            "101200100": "武汉",
            "101190400": "苏州",
            "101210100": "杭州"
        }
    
    async def initialize(self):
        """初始化爬虫"""
        try:
            logger.info("🔧 初始化BOSS爬虫...")
            
            # 启动Playwright
            self.playwright = await async_playwright().start()
            
            # 配置浏览器启动参数
            browser_args = self._get_browser_args()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(**browser_args)
            
            # 创建浏览器上下文
            context_options = await self._get_context_options()
            self.context = await self.browser.new_context(**context_options)
            
            # 设置反检测
            await self._setup_anti_detection()
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置页面配置
            await self._setup_page()
            
            self.is_initialized = True
            logger.success("✅ BOSS爬虫初始化完成")
            
        except Exception as e:
            logger.error(f"❌ BOSS爬虫初始化失败: {e}")
            raise
    
    def _get_browser_args(self) -> Dict[str, Any]:
        """获取浏览器启动参数"""
        browser_config = self.anti_crawler_config['browser']
        
        args = [
            '--no-sandbox',
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
        ]
        
        # 代理设置
        if self.proxy_manager and self.proxy_manager.get_current_proxy():
            proxy_url = self.proxy_manager.get_current_proxy()
            args.append(f'--proxy-server={proxy_url}')
        
        return {
            'headless': browser_config.get('headless', False),
            'args': args,
            'ignore_default_args': ['--enable-automation'],
        }
    
    async def _get_context_options(self) -> Dict[str, Any]:
        """获取浏览器上下文选项"""
        browser_config = self.anti_crawler_config['browser']
        
        options = {
            'viewport': {
                'width': browser_config['window_size'][0],
                'height': browser_config['window_size'][1]
            },
            'user_agent': self.ua_manager.get_random_ua(),
            'locale': 'zh-CN',
            'timezone_id': 'Asia/Shanghai',
        }
        
        return options
    
    async def _setup_anti_detection(self):
        """设置反检测"""
        # 注入反检测脚本
        await self.context.add_init_script("""
            // 移除webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造chrome属性
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            // 伪造权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 伪造插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪造语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
        """)
    
    async def _setup_page(self):
        """设置页面配置"""
        # 设置超时
        self.page.set_default_timeout(30000)
        self.page.set_default_navigation_timeout(30000)
        
        # 拦截图片和CSS（可选，提高速度）
        if self.anti_crawler_config['browser'].get('disable_images', False):
            await self.page.route("**/*.{png,jpg,jpeg,gif,svg,css}", lambda route: route.abort())
        
        # 监听控制台消息
        self.page.on("console", lambda msg: logger.debug(f"Console: {msg.text}"))
        
        # 监听页面错误
        self.page.on("pageerror", lambda error: logger.warning(f"Page error: {error}"))
    
    async def search_jobs(self, city_code: str, keyword: str) -> List[str]:
        """
        搜索岗位列表
        
        Args:
            city_code: 城市代码
            keyword: 搜索关键词
            
        Returns:
            岗位URL列表
        """
        try:
            logger.info(f"🔍 搜索岗位: {self.city_mapping.get(city_code, city_code)} - {keyword}")
            
            # 构建搜索URL
            search_url = self._build_search_url(city_code, keyword)
            logger.debug(f"搜索URL: {search_url}")
            
            # 访问搜索页面
            await self.page.goto(search_url, wait_until='networkidle')
            
            # 检查是否需要处理验证码
            if await self._check_captcha():
                await self._handle_captcha()
            
            # 等待页面加载
            await self.page.wait_for_selector('.job-list-box', timeout=10000)
            
            # 获取岗位链接
            job_urls = await self._extract_job_urls()
            
            logger.info(f"✅ 找到 {len(job_urls)} 个岗位")
            return job_urls
            
        except Exception as e:
            logger.error(f"❌ 搜索岗位失败: {e}")
            return []
    
    def _build_search_url(self, city_code: str, keyword: str) -> str:
        """构建搜索URL"""
        base_url = self.boss_config['search_url']
        
        params = {
            'query': keyword,
            'city': city_code,
            'position': '',
            'salary': '',
            'experience': '',
            'degree': '',
            'industry': '',
            'scale': '',
            'stage': '',
            'jobType': '',
            'page': '1'
        }
        
        # 添加薪资范围
        salary_range = self.boss_config['search_params']['salary_range']
        if salary_range and len(salary_range) >= 2:
            params['salary'] = f"{salary_range[0]},{salary_range[1]}"
        
        # 构建URL
        param_str = '&'.join([f"{k}={v}" for k, v in params.items() if v])
        return f"{base_url}?{param_str}"
    
    async def _check_captcha(self) -> bool:
        """检查是否出现验证码"""
        try:
            # 检查常见的验证码元素
            captcha_selectors = [
                '.captcha-container',
                '.verify-code',
                '.slider-container',
                '#captcha',
                '.geetest_holder'
            ]
            
            for selector in captcha_selectors:
                if await self.page.query_selector(selector):
                    logger.warning("⚠️ 检测到验证码")
                    return True
            
            return False
            
        except Exception:
            return False
    
    async def _handle_captcha(self):
        """处理验证码"""
        try:
            logger.info("🔐 开始处理验证码...")
            
            if self.captcha_solver:
                # 使用自动验证码解决器
                success = await self.captcha_solver.solve_captcha(self.page)
                if success:
                    logger.success("✅ 验证码处理成功")
                    return
            
            # 手动处理验证码
            if self.anti_crawler_config['captcha'].get('manual_solve', False):
                logger.info("⏳ 请手动完成验证码，程序将等待...")
                
                # 等待验证码消失
                max_wait = 60  # 最大等待60秒
                for _ in range(max_wait):
                    if not await self._check_captcha():
                        logger.success("✅ 验证码已完成")
                        return
                    await asyncio.sleep(1)
                
                logger.warning("⚠️ 验证码处理超时")
            
        except Exception as e:
            logger.error(f"❌ 处理验证码失败: {e}")
    
    async def _extract_job_urls(self) -> List[str]:
        """提取岗位URL列表"""
        try:
            job_urls = []
            
            # 获取页面内容
            content = await self.page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找岗位链接
            job_links = soup.find_all('a', class_='job-card-left')
            
            for link in job_links:
                href = link.get('href')
                if href:
                    # 构建完整URL
                    full_url = urljoin(self.boss_config['base_url'], href)
                    
                    # 验证URL
                    if self.url_validator.is_valid_boss_job_url(full_url):
                        job_urls.append(full_url)
            
            # 去重
            job_urls = list(set(job_urls))
            
            return job_urls
            
        except Exception as e:
            logger.error(f"❌ 提取岗位URL失败: {e}")
            return []
    
    async def crawl_job_detail(self, job_url: str) -> Optional[Dict[str, Any]]:
        """
        爬取岗位详情
        
        Args:
            job_url: 岗位详情页URL
            
        Returns:
            岗位详情数据
        """
        try:
            logger.debug(f"📋 爬取岗位详情: {job_url}")
            
            # 访问岗位详情页
            await self.page.goto(job_url, wait_until='networkidle')
            
            # 检查验证码
            if await self._check_captcha():
                await self._handle_captcha()
            
            # 等待页面加载
            await self.page.wait_for_selector('.job-detail', timeout=10000)
            
            # 提取岗位数据
            job_data = await self._extract_job_data()
            
            if job_data:
                job_data['job_url'] = job_url
                logger.debug(f"✅ 岗位数据提取成功: {job_data.get('job_title', 'N/A')}")
            
            return job_data
            
        except Exception as e:
            logger.error(f"❌ 爬取岗位详情失败 {job_url}: {e}")
            return None
    
    async def _extract_job_data(self) -> Optional[Dict[str, Any]]:
        """提取岗位数据"""
        try:
            # 获取页面内容
            content = await self.page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            job_data = {}
            
            # 提取岗位名称
            job_title_elem = soup.find('h1', class_='name')
            job_data['job_title'] = job_title_elem.get_text(strip=True) if job_title_elem else ''
            
            # 提取薪资信息
            salary_elem = soup.find('span', class_='salary')
            job_data['salary_info'] = salary_elem.get_text(strip=True) if salary_elem else ''
            
            # 提取工作经验要求
            experience_elem = soup.find('span', class_='experience')
            job_data['experience_req'] = experience_elem.get_text(strip=True) if experience_elem else ''
            
            # 提取学历要求
            education_elem = soup.find('span', class_='education')
            job_data['education_req'] = education_elem.get_text(strip=True) if education_elem else ''
            
            # 提取工作地址
            location_elem = soup.find('span', class_='location-address')
            job_data['work_location'] = location_elem.get_text(strip=True) if location_elem else ''
            
            # 提取岗位描述
            desc_elem = soup.find('div', class_='job-sec-text')
            job_data['job_description'] = desc_elem.get_text(strip=True) if desc_elem else ''
            
            # 提取公司信息
            company_elem = soup.find('div', class_='company-info')
            if company_elem:
                company_name = company_elem.find('h3', class_='name')
                job_data['company_name'] = company_name.get_text(strip=True) if company_name else ''
                
                company_desc = company_elem.find('div', class_='company-desc')
                job_data['company_info'] = company_desc.get_text(strip=True) if company_desc else ''
            
            # 提取福利待遇
            benefits_elem = soup.find('div', class_='job-tags')
            if benefits_elem:
                benefit_tags = benefits_elem.find_all('span', class_='tag')
                benefits = [tag.get_text(strip=True) for tag in benefit_tags]
                job_data['benefits'] = ', '.join(benefits)
            else:
                job_data['benefits'] = ''
            
            return job_data
            
        except Exception as e:
            logger.error(f"❌ 提取岗位数据失败: {e}")
            return None
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            
            if self.context:
                await self.context.close()
            
            if self.browser:
                await self.browser.close()
            
            if self.playwright:
                await self.playwright.stop()
            
            logger.info("✅ BOSS爬虫资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理BOSS爬虫资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'playwright') and self.playwright:
            try:
                asyncio.create_task(self.cleanup())
            except Exception:
                pass
