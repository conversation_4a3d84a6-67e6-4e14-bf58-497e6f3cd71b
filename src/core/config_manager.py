#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 负责加载和管理系统配置
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger
from dotenv import load_dotenv


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，默认为项目根目录下的config.yaml
        """
        self.project_root = Path(__file__).parent.parent.parent
        self.config_path = config_path or self.project_root / "config.yaml"
        self.env_path = self.project_root / ".env"
        
        # 加载环境变量
        self._load_environment()
        
        # 加载配置文件
        self.config = self._load_config()
        
        # 处理环境变量替换
        self._process_environment_variables()
        
        # 验证配置
        self._validate_config()
    
    def _load_environment(self):
        """加载环境变量"""
        try:
            if self.env_path.exists():
                load_dotenv(self.env_path)
                logger.info(f"✅ 已加载环境变量文件: {self.env_path}")
            else:
                logger.warning(f"⚠️ 环境变量文件不存在: {self.env_path}")
        except Exception as e:
            logger.error(f"❌ 加载环境变量失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"✅ 已加载配置文件: {self.config_path}")
            return config
            
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {e}")
            raise
    
    def _process_environment_variables(self):
        """处理配置中的环境变量替换"""
        def replace_env_vars(obj):
            if isinstance(obj, dict):
                return {k: replace_env_vars(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [replace_env_vars(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
                env_var = obj[2:-1]
                return os.getenv(env_var, obj)
            else:
                return obj
        
        self.config = replace_env_vars(self.config)
    
    def _validate_config(self):
        """验证配置的有效性"""
        required_sections = ['app', 'boss', 'anti_crawler', 'data_extraction', 'storage']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必需的节: {section}")
        
        # 验证BOSS配置
        boss_config = self.config['boss']
        if 'search_params' not in boss_config:
            raise ValueError("BOSS配置缺少search_params节")
        
        search_params = boss_config['search_params']
        if not search_params.get('city_codes'):
            raise ValueError("必须配置至少一个城市代码")
        
        if not search_params.get('keywords'):
            raise ValueError("必须配置至少一个搜索关键词")
        
        # 验证代理配置
        if self.config['proxy']['enabled']:
            if not self.config['proxy'].get('http_proxy'):
                logger.warning("⚠️ 代理已启用但未配置http_proxy")
        
        logger.info("✅ 配置验证通过")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的路径
        
        Args:
            key_path: 配置键路径，如 'boss.search_params.keywords'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 要设置的值
        """
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def save_config(self, path: Optional[str] = None):
        """
        保存配置到文件
        
        Args:
            path: 保存路径，默认为原配置文件路径
        """
        save_path = path or self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"✅ 配置已保存到: {save_path}")
            
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            raise
    
    def get_city_codes(self) -> list:
        """获取城市代码列表"""
        return self.get('boss.search_params.city_codes', [])
    
    def get_keywords(self) -> list:
        """获取搜索关键词列表"""
        return self.get('boss.search_params.keywords', [])
    
    def get_salary_range(self) -> list:
        """获取薪资范围"""
        return self.get('boss.search_params.salary_range', [10, 50])
    
    def is_proxy_enabled(self) -> bool:
        """检查是否启用代理"""
        return self.get('proxy.enabled', False)
    
    def get_proxy_config(self) -> Dict[str, str]:
        """获取代理配置"""
        if not self.is_proxy_enabled():
            return {}
        
        return {
            'http': self.get('proxy.http_proxy'),
            'https': self.get('proxy.https_proxy')
        }
    
    def is_ai_enabled(self) -> bool:
        """检查是否启用AI功能"""
        return self.get('ai.enabled', False)
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return self.get('ai', {})
    
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        return self.get('storage', {})
    
    def get_anti_crawler_config(self) -> Dict[str, Any]:
        """获取反爬虫配置"""
        return self.get('anti_crawler', {})
    
    def is_debug_mode(self) -> bool:
        """检查是否为调试模式"""
        return self.get('app.debug', False)
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.get('app.log_level', 'INFO')
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        return f"ConfigManager(config_path={self.config_path})"
    
    def __repr__(self) -> str:
        """返回配置的详细表示"""
        return self.__str__()
