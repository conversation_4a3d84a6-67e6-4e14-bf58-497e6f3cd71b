#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫管理器 - 核心爬虫逻辑管理
基于GitHub最佳实践，集成反爬虫和AI技术
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
from pathlib import Path

from .config_manager import ConfigManager
from ..crawlers.boss_crawler import BossCrawler
from ..anti_crawler.proxy_manager import ProxyManager
from ..anti_crawler.captcha_solver import CaptchaSolver
from ..data.data_processor import DataProcessor
from ..data.excel_exporter import ExcelExporter
from ..ai.job_analyzer import JobAnalyzer
from ..utils.progress_tracker import ProgressTracker


class CrawlerManager:
    """爬虫管理器 - 统一管理所有爬虫组件"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化爬虫管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.config = config_manager.config
        
        # 核心组件
        self.boss_crawler = None
        self.proxy_manager = None
        self.captcha_solver = None
        self.data_processor = None
        self.excel_exporter = None
        self.job_analyzer = None
        self.progress_tracker = None
        
        # 运行状态
        self.is_running = False
        self.start_time = None
        self.total_processed = 0
        self.valid_jobs = 0
        
        # 数据存储
        self.collected_jobs = []
        self.failed_urls = []
        
    async def initialize(self):
        """初始化所有组件"""
        try:
            logger.info("🔧 初始化爬虫管理器组件...")
            
            # 初始化代理管理器
            if self.config['proxy']['enabled']:
                self.proxy_manager = ProxyManager(self.config['proxy'])
                await self.proxy_manager.initialize()
                logger.info("✅ 代理管理器初始化完成")
            
            # 初始化验证码解决器
            if self.config['anti_crawler']['captcha']['enabled']:
                self.captcha_solver = CaptchaSolver(self.config['anti_crawler']['captcha'])
                await self.captcha_solver.initialize()
                logger.info("✅ 验证码解决器初始化完成")
            
            # 初始化数据处理器
            self.data_processor = DataProcessor(self.config['data_extraction'])
            logger.info("✅ 数据处理器初始化完成")
            
            # 初始化Excel导出器
            self.excel_exporter = ExcelExporter(self.config['storage']['excel'])
            logger.info("✅ Excel导出器初始化完成")
            
            # 初始化AI分析器
            if self.config['ai']['enabled']:
                self.job_analyzer = JobAnalyzer(self.config['ai'])
                await self.job_analyzer.initialize()
                logger.info("✅ AI分析器初始化完成")
            
            # 初始化进度跟踪器
            self.progress_tracker = ProgressTracker()
            logger.info("✅ 进度跟踪器初始化完成")
            
            # 初始化BOSS爬虫
            self.boss_crawler = BossCrawler(
                config=self.config,
                proxy_manager=self.proxy_manager,
                captcha_solver=self.captcha_solver,
                data_processor=self.data_processor
            )
            await self.boss_crawler.initialize()
            logger.info("✅ BOSS爬虫初始化完成")
            
            logger.success("🎉 所有组件初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 组件初始化失败: {e}")
            raise
    
    async def start_crawling(self) -> Dict[str, Any]:
        """
        开始爬取任务
        
        Returns:
            爬取结果统计
        """
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("🚀 开始BOSS直聘数据爬取任务")
            
            # 获取搜索参数
            city_codes = self.config_manager.get_city_codes()
            keywords = self.config_manager.get_keywords()
            
            total_tasks = len(city_codes) * len(keywords)
            logger.info(f"📊 总计任务数: {total_tasks} (城市: {len(city_codes)}, 关键词: {len(keywords)})")
            
            # 开始进度跟踪
            self.progress_tracker.start(total_tasks)
            
            # 执行爬取任务
            for city_code in city_codes:
                for keyword in keywords:
                    if not self.is_running:
                        break
                    
                    await self._crawl_city_keyword(city_code, keyword)
                    
                    # 实时保存检查
                    await self._check_auto_save()
                    
                    # 任务间隔
                    await self._task_delay()
                
                if not self.is_running:
                    break
            
            # 最终保存
            await self._final_save()
            
            # 生成结果统计
            results = self._generate_results()
            
            logger.success(f"🎉 爬取任务完成! 共处理 {self.total_processed} 个岗位")
            return results
            
        except Exception as e:
            logger.error(f"❌ 爬取任务失败: {e}")
            raise
        finally:
            self.is_running = False
    
    async def _crawl_city_keyword(self, city_code: str, keyword: str):
        """
        爬取指定城市和关键词的岗位
        
        Args:
            city_code: 城市代码
            keyword: 搜索关键词
        """
        try:
            logger.info(f"🔍 开始爬取: 城市={city_code}, 关键词={keyword}")
            
            # 执行搜索和爬取
            jobs = await self.boss_crawler.search_jobs(city_code, keyword)
            
            if not jobs:
                logger.warning(f"⚠️ 未找到相关岗位: {city_code} - {keyword}")
                return
            
            logger.info(f"📋 找到 {len(jobs)} 个岗位，开始详细爬取...")
            
            # 处理每个岗位
            for job_url in jobs:
                if not self.is_running:
                    break
                
                try:
                    # 爬取岗位详情
                    job_data = await self.boss_crawler.crawl_job_detail(job_url)
                    
                    if job_data:
                        # 数据验证和处理
                        processed_data = await self._process_job_data(job_data)
                        
                        if processed_data:
                            self.collected_jobs.append(processed_data)
                            self.valid_jobs += 1
                            
                            logger.info(f"✅ 成功爬取岗位: {processed_data.get('job_title', 'N/A')}")
                        else:
                            logger.warning(f"⚠️ 岗位数据验证失败: {job_url}")
                    else:
                        self.failed_urls.append(job_url)
                        logger.warning(f"⚠️ 岗位爬取失败: {job_url}")
                    
                    self.total_processed += 1
                    self.progress_tracker.update(1)
                    
                    # 请求间隔
                    await self._request_delay()
                    
                except Exception as e:
                    logger.error(f"❌ 处理岗位时发生错误 {job_url}: {e}")
                    self.failed_urls.append(job_url)
                    continue
            
            logger.info(f"✅ 完成爬取: {city_code} - {keyword}")
            
        except Exception as e:
            logger.error(f"❌ 爬取城市关键词失败 {city_code}-{keyword}: {e}")
    
    async def _process_job_data(self, job_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理岗位数据
        
        Args:
            job_data: 原始岗位数据
            
        Returns:
            处理后的岗位数据
        """
        try:
            # 数据验证
            if not self.data_processor.validate_job_data(job_data):
                return None
            
            # 数据清洗和标准化
            processed_data = self.data_processor.process_job_data(job_data)
            
            # AI分析（如果启用）
            if self.job_analyzer:
                ai_analysis = await self.job_analyzer.analyze_job(processed_data)
                processed_data.update(ai_analysis)
            
            # 添加时间戳
            processed_data['crawl_time'] = datetime.now().isoformat()
            
            return processed_data
            
        except Exception as e:
            logger.error(f"❌ 处理岗位数据失败: {e}")
            return None
    
    async def _check_auto_save(self):
        """检查是否需要自动保存"""
        auto_save_interval = self.config['storage']['excel']['auto_save_interval']
        
        if len(self.collected_jobs) >= auto_save_interval:
            await self._save_data()
    
    async def _save_data(self):
        """保存数据到Excel"""
        try:
            if not self.collected_jobs:
                return
            
            logger.info(f"💾 保存 {len(self.collected_jobs)} 条数据到Excel...")
            
            # 导出到Excel
            output_file = await self.excel_exporter.export_jobs(self.collected_jobs)
            
            logger.success(f"✅ 数据已保存到: {output_file}")
            
            # 清空已保存的数据（可选，根据需求）
            # self.collected_jobs.clear()
            
        except Exception as e:
            logger.error(f"❌ 保存数据失败: {e}")
    
    async def _final_save(self):
        """最终保存所有数据"""
        if self.collected_jobs:
            await self._save_data()
    
    async def _task_delay(self):
        """任务间延迟"""
        delay_config = self.config['anti_crawler']['request_delay']
        delay = delay_config['min'] + (delay_config['max'] - delay_config['min']) * 0.5
        await asyncio.sleep(delay)
    
    async def _request_delay(self):
        """请求间延迟"""
        delay_config = self.config['anti_crawler']['request_delay']
        import random
        delay = random.uniform(delay_config['min'], delay_config['max'])
        await asyncio.sleep(delay)
    
    def _generate_results(self) -> Dict[str, Any]:
        """生成结果统计"""
        end_time = datetime.now()
        duration = end_time - self.start_time if self.start_time else None
        
        return {
            'total_jobs': self.total_processed,
            'valid_jobs': self.valid_jobs,
            'saved_jobs': len(self.collected_jobs),
            'failed_urls': len(self.failed_urls),
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': end_time.isoformat(),
            'duration': str(duration) if duration else None,
            'output_file': self.excel_exporter.get_output_file() if self.excel_exporter else None
        }
    
    async def stop_crawling(self):
        """停止爬取"""
        logger.info("⏹️ 正在停止爬取...")
        self.is_running = False
        
        # 保存当前数据
        await self._final_save()
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 开始清理资源...")
            
            if self.boss_crawler:
                await self.boss_crawler.cleanup()
            
            if self.proxy_manager:
                await self.proxy_manager.cleanup()
            
            if self.captcha_solver:
                await self.captcha_solver.cleanup()
            
            if self.job_analyzer:
                await self.job_analyzer.cleanup()
            
            logger.info("✅ 资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理资源失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            'is_running': self.is_running,
            'total_processed': self.total_processed,
            'valid_jobs': self.valid_jobs,
            'collected_jobs': len(self.collected_jobs),
            'failed_urls': len(self.failed_urls),
            'start_time': self.start_time.isoformat() if self.start_time else None
        }
