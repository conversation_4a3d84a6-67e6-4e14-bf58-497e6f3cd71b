#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查模块 - 检查系统环境和依赖
"""

import sys
import os
import platform
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Any
from loguru import logger
import importlib.util


def check_environment() -> bool:
    """
    检查系统环境
    
    Returns:
        是否通过检查
    """
    try:
        logger.info("🔍 开始环境检查...")
        
        checks = [
            ("Python版本", check_python_version),
            ("必需依赖包", check_required_packages),
            ("Chrome浏览器", check_chrome_browser),
            ("系统权限", check_system_permissions),
            ("网络连接", check_network_connection),
            ("磁盘空间", check_disk_space),
            ("配置文件", check_config_files)
        ]
        
        all_passed = True
        results = []
        
        for check_name, check_func in checks:
            try:
                passed, message = check_func()
                results.append((check_name, passed, message))
                
                if passed:
                    logger.info(f"✅ {check_name}: {message}")
                else:
                    logger.error(f"❌ {check_name}: {message}")
                    all_passed = False
                    
            except Exception as e:
                logger.error(f"❌ {check_name} 检查失败: {e}")
                results.append((check_name, False, str(e)))
                all_passed = False
        
        # 显示检查结果摘要
        _show_check_summary(results)
        
        if all_passed:
            logger.success("🎉 环境检查全部通过")
        else:
            logger.error("💥 环境检查存在问题，请解决后重试")
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 环境检查失败: {e}")
        return False


def check_python_version() -> Tuple[bool, str]:
    """
    检查Python版本
    
    Returns:
        (是否通过, 消息)
    """
    try:
        version = sys.version_info
        
        if version.major != 3:
            return False, f"需要Python 3.x，当前版本: {version.major}.{version.minor}.{version.micro}"
        
        if version.minor < 8:
            return False, f"需要Python 3.8+，当前版本: {version.major}.{version.minor}.{version.micro}"
        
        return True, f"Python {version.major}.{version.minor}.{version.micro}"
        
    except Exception as e:
        return False, f"检查Python版本失败: {e}"


def check_required_packages() -> Tuple[bool, str]:
    """
    检查必需的依赖包
    
    Returns:
        (是否通过, 消息)
    """
    try:
        required_packages = [
            'requests',
            'selenium',
            'playwright',
            'beautifulsoup4',
            'lxml',
            'pandas',
            'openpyxl',
            'loguru',
            'pyyaml',
            'aiohttp',
            'ddddocr',
            'pillow',
            'fake_useragent'
        ]
        
        missing_packages = []
        installed_packages = []
        
        for package in required_packages:
            try:
                # 特殊处理一些包名
                import_name = {
                    'beautifulsoup4': 'bs4',
                    'pillow': 'PIL',
                    'fake_useragent': 'fake_useragent'
                }.get(package, package)
                
                spec = importlib.util.find_spec(import_name)
                if spec is None:
                    missing_packages.append(package)
                else:
                    installed_packages.append(package)
                    
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            return False, f"缺少依赖包: {', '.join(missing_packages)}"
        
        return True, f"已安装 {len(installed_packages)} 个必需包"
        
    except Exception as e:
        return False, f"检查依赖包失败: {e}"


def check_chrome_browser() -> Tuple[bool, str]:
    """
    检查Chrome浏览器
    
    Returns:
        (是否通过, 消息)
    """
    try:
        system = platform.system().lower()
        
        # Chrome可执行文件路径
        chrome_paths = {
            'windows': [
                r'C:\Program Files\Google\Chrome\Application\chrome.exe',
                r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
                r'%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe'
            ],
            'darwin': [
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
            ],
            'linux': [
                '/usr/bin/google-chrome',
                '/usr/bin/google-chrome-stable',
                '/usr/bin/chromium-browser',
                '/snap/bin/chromium'
            ]
        }
        
        paths_to_check = chrome_paths.get(system, [])
        
        for path in paths_to_check:
            # 展开环境变量
            expanded_path = os.path.expandvars(path)
            if os.path.exists(expanded_path):
                try:
                    # 尝试获取Chrome版本
                    result = subprocess.run(
                        [expanded_path, '--version'],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        return True, f"找到Chrome: {version}"
                        
                except subprocess.TimeoutExpired:
                    return True, f"找到Chrome: {expanded_path}"
                except Exception:
                    continue
        
        # 尝试通过命令行检查
        try:
            result = subprocess.run(
                ['google-chrome', '--version'] if system != 'windows' else ['chrome', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                version = result.stdout.strip()
                return True, f"Chrome版本: {version}"
                
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        return False, "未找到Chrome浏览器，请安装Google Chrome"
        
    except Exception as e:
        return False, f"检查Chrome浏览器失败: {e}"


def check_system_permissions() -> Tuple[bool, str]:
    """
    检查系统权限
    
    Returns:
        (是否通过, 消息)
    """
    try:
        # 检查当前目录写权限
        current_dir = Path.cwd()
        test_file = current_dir / "test_write_permission.tmp"
        
        try:
            test_file.write_text("test")
            test_file.unlink()
            write_permission = True
        except Exception:
            write_permission = False
        
        if not write_permission:
            return False, "当前目录没有写权限"
        
        # 检查创建子目录权限
        test_dir = current_dir / "test_dir_permission"
        try:
            test_dir.mkdir(exist_ok=True)
            test_dir.rmdir()
            dir_permission = True
        except Exception:
            dir_permission = False
        
        if not dir_permission:
            return False, "无法创建子目录"
        
        return True, "系统权限正常"
        
    except Exception as e:
        return False, f"检查系统权限失败: {e}"


def check_network_connection() -> Tuple[bool, str]:
    """
    检查网络连接
    
    Returns:
        (是否通过, 消息)
    """
    try:
        import socket
        
        # 测试DNS解析
        try:
            socket.gethostbyname('www.baidu.com')
            dns_ok = True
        except socket.gaierror:
            dns_ok = False
        
        if not dns_ok:
            return False, "DNS解析失败"
        
        # 测试HTTP连接
        try:
            import requests
            response = requests.get('https://www.baidu.com', timeout=10)
            http_ok = response.status_code == 200
        except Exception:
            http_ok = False
        
        if not http_ok:
            return False, "HTTP连接失败"
        
        return True, "网络连接正常"
        
    except Exception as e:
        return False, f"检查网络连接失败: {e}"


def check_disk_space() -> Tuple[bool, str]:
    """
    检查磁盘空间
    
    Returns:
        (是否通过, 消息)
    """
    try:
        import shutil
        
        current_dir = Path.cwd()
        total, used, free = shutil.disk_usage(current_dir)
        
        # 转换为GB
        free_gb = free / (1024 ** 3)
        total_gb = total / (1024 ** 3)
        
        # 至少需要1GB空闲空间
        min_required_gb = 1.0
        
        if free_gb < min_required_gb:
            return False, f"磁盘空间不足，需要至少{min_required_gb}GB，当前可用{free_gb:.2f}GB"
        
        return True, f"磁盘空间充足，可用{free_gb:.2f}GB / 总计{total_gb:.2f}GB"
        
    except Exception as e:
        return False, f"检查磁盘空间失败: {e}"


def check_config_files() -> Tuple[bool, str]:
    """
    检查配置文件
    
    Returns:
        (是否通过, 消息)
    """
    try:
        current_dir = Path.cwd()
        
        # 检查必需的配置文件
        required_files = [
            'config.yaml',
            'requirements.txt'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_name in required_files:
            file_path = current_dir / file_name
            if file_path.exists():
                existing_files.append(file_name)
            else:
                missing_files.append(file_name)
        
        if missing_files:
            return False, f"缺少配置文件: {', '.join(missing_files)}"
        
        # 检查config.yaml格式
        try:
            import yaml
            config_path = current_dir / 'config.yaml'
            with open(config_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
        except Exception as e:
            return False, f"config.yaml格式错误: {e}"
        
        return True, f"配置文件完整: {', '.join(existing_files)}"
        
    except Exception as e:
        return False, f"检查配置文件失败: {e}"


def _show_check_summary(results: List[Tuple[str, bool, str]]):
    """
    显示检查结果摘要
    
    Args:
        results: 检查结果列表
    """
    try:
        passed_count = sum(1 for _, passed, _ in results if passed)
        total_count = len(results)
        
        logger.info("=" * 60)
        logger.info("📊 环境检查摘要:")
        logger.info(f"   ✅ 通过: {passed_count}")
        logger.info(f"   ❌ 失败: {total_count - passed_count}")
        logger.info(f"   📊 总计: {total_count}")
        logger.info("=" * 60)
        
        # 显示失败的检查项
        failed_checks = [name for name, passed, _ in results if not passed]
        if failed_checks:
            logger.warning("⚠️ 需要解决的问题:")
            for check_name in failed_checks:
                logger.warning(f"   • {check_name}")
        
    except Exception as e:
        logger.error(f"❌ 显示检查摘要失败: {e}")


def get_system_info() -> Dict[str, Any]:
    """
    获取系统信息
    
    Returns:
        系统信息字典
    """
    try:
        import psutil
        
        return {
            'platform': platform.platform(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': sys.version,
            'python_executable': sys.executable,
            'cpu_count': os.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024 ** 3), 2),
            'memory_available_gb': round(psutil.virtual_memory().available / (1024 ** 3), 2),
            'disk_usage': {
                'total_gb': round(psutil.disk_usage('/').total / (1024 ** 3), 2),
                'free_gb': round(psutil.disk_usage('/').free / (1024 ** 3), 2)
            }
        }
        
    except Exception as e:
        logger.error(f"❌ 获取系统信息失败: {e}")
        return {
            'platform': platform.platform(),
            'system': platform.system(),
            'python_version': sys.version,
            'error': str(e)
        }


def install_missing_packages(packages: List[str]) -> bool:
    """
    安装缺失的包
    
    Args:
        packages: 包名列表
        
    Returns:
        是否成功
    """
    try:
        logger.info(f"📦 开始安装缺失的包: {', '.join(packages)}")
        
        for package in packages:
            try:
                subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', package],
                    check=True,
                    capture_output=True,
                    text=True
                )
                logger.info(f"✅ 成功安装: {package}")
                
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ 安装失败 {package}: {e.stderr}")
                return False
        
        logger.success("🎉 所有包安装完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 安装包失败: {e}")
        return False
