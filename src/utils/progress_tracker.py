#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度跟踪器 - 跟踪和显示任务进度
"""

import time
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from loguru import logger
from tqdm import tqdm
import threading


class ProgressTracker:
    """进度跟踪器类"""
    
    def __init__(self, show_progress_bar: bool = True):
        """
        初始化进度跟踪器
        
        Args:
            show_progress_bar: 是否显示进度条
        """
        self.show_progress_bar = show_progress_bar
        
        # 进度状态
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        self.start_time = None
        self.end_time = None
        
        # 进度条
        self.progress_bar = None
        
        # 统计信息
        self.task_stats = {
            'total': 0,
            'completed': 0,
            'failed': 0,
            'success_rate': 0.0,
            'avg_time_per_task': 0.0,
            'estimated_remaining_time': 0.0
        }
        
        # 任务时间记录
        self.task_times = []
        self.last_update_time = None
        
        # 线程锁
        self.lock = threading.Lock()
    
    def start(self, total_tasks: int, description: str = "处理中"):
        """
        开始跟踪
        
        Args:
            total_tasks: 总任务数
            description: 描述
        """
        try:
            with self.lock:
                self.total_tasks = total_tasks
                self.completed_tasks = 0
                self.failed_tasks = 0
                self.start_time = datetime.now()
                self.end_time = None
                self.task_times.clear()
                
                if self.show_progress_bar and total_tasks > 0:
                    self.progress_bar = tqdm(
                        total=total_tasks,
                        desc=description,
                        unit="个",
                        ncols=100,
                        bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]'
                    )
                
                self._update_stats()
                
                logger.info(f"🚀 开始进度跟踪: {description} (总计: {total_tasks})")
                
        except Exception as e:
            logger.error(f"❌ 启动进度跟踪失败: {e}")
    
    def update(self, increment: int = 1, failed: int = 0):
        """
        更新进度
        
        Args:
            increment: 完成任务数增量
            failed: 失败任务数增量
        """
        try:
            with self.lock:
                current_time = datetime.now()
                
                # 记录任务时间
                if self.last_update_time:
                    task_duration = (current_time - self.last_update_time).total_seconds()
                    self.task_times.append(task_duration)
                    
                    # 保持最近100个任务的时间记录
                    if len(self.task_times) > 100:
                        self.task_times = self.task_times[-100:]
                
                self.last_update_time = current_time
                
                # 更新计数
                self.completed_tasks += increment
                self.failed_tasks += failed
                
                # 更新进度条
                if self.progress_bar:
                    self.progress_bar.update(increment + failed)
                    
                    # 更新进度条描述
                    success_rate = self.get_success_rate()
                    self.progress_bar.set_postfix({
                        '成功率': f'{success_rate:.1f}%',
                        '失败': self.failed_tasks
                    })
                
                # 更新统计信息
                self._update_stats()
                
                # 定期输出日志
                total_processed = self.completed_tasks + self.failed_tasks
                if total_processed % 10 == 0 or total_processed == self.total_tasks:
                    self._log_progress()
                
        except Exception as e:
            logger.error(f"❌ 更新进度失败: {e}")
    
    def finish(self):
        """完成跟踪"""
        try:
            with self.lock:
                self.end_time = datetime.now()
                
                if self.progress_bar:
                    self.progress_bar.close()
                    self.progress_bar = None
                
                self._update_stats()
                self._log_final_stats()
                
        except Exception as e:
            logger.error(f"❌ 完成进度跟踪失败: {e}")
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            total_processed = self.completed_tasks + self.failed_tasks
            
            self.task_stats.update({
                'total': self.total_tasks,
                'completed': self.completed_tasks,
                'failed': self.failed_tasks,
                'processed': total_processed,
                'success_rate': self.get_success_rate(),
                'avg_time_per_task': self.get_avg_time_per_task(),
                'estimated_remaining_time': self.get_estimated_remaining_time()
            })
            
        except Exception as e:
            logger.error(f"❌ 更新统计信息失败: {e}")
    
    def _log_progress(self):
        """输出进度日志"""
        try:
            total_processed = self.completed_tasks + self.failed_tasks
            progress_percent = (total_processed / self.total_tasks * 100) if self.total_tasks > 0 else 0
            
            logger.info(
                f"📊 进度: {total_processed}/{self.total_tasks} "
                f"({progress_percent:.1f}%) | "
                f"成功: {self.completed_tasks} | "
                f"失败: {self.failed_tasks} | "
                f"成功率: {self.get_success_rate():.1f}%"
            )
            
        except Exception as e:
            logger.error(f"❌ 输出进度日志失败: {e}")
    
    def _log_final_stats(self):
        """输出最终统计"""
        try:
            duration = self.get_total_duration()
            
            logger.info("=" * 60)
            logger.success("🎉 任务完成统计:")
            logger.info(f"   📊 总任务数: {self.total_tasks}")
            logger.info(f"   ✅ 成功完成: {self.completed_tasks}")
            logger.info(f"   ❌ 失败任务: {self.failed_tasks}")
            logger.info(f"   📈 成功率: {self.get_success_rate():.2f}%")
            logger.info(f"   ⏱️  总耗时: {duration}")
            logger.info(f"   ⚡ 平均速度: {self.get_avg_time_per_task():.2f}秒/任务")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"❌ 输出最终统计失败: {e}")
    
    def get_success_rate(self) -> float:
        """
        获取成功率
        
        Returns:
            成功率百分比
        """
        try:
            total_processed = self.completed_tasks + self.failed_tasks
            if total_processed == 0:
                return 0.0
            
            return (self.completed_tasks / total_processed) * 100
            
        except Exception as e:
            logger.error(f"❌ 计算成功率失败: {e}")
            return 0.0
    
    def get_avg_time_per_task(self) -> float:
        """
        获取平均任务时间
        
        Returns:
            平均时间（秒）
        """
        try:
            if not self.task_times:
                return 0.0
            
            return sum(self.task_times) / len(self.task_times)
            
        except Exception as e:
            logger.error(f"❌ 计算平均任务时间失败: {e}")
            return 0.0
    
    def get_estimated_remaining_time(self) -> float:
        """
        获取预估剩余时间
        
        Returns:
            剩余时间（秒）
        """
        try:
            if not self.task_times or self.total_tasks == 0:
                return 0.0
            
            total_processed = self.completed_tasks + self.failed_tasks
            remaining_tasks = self.total_tasks - total_processed
            
            if remaining_tasks <= 0:
                return 0.0
            
            avg_time = self.get_avg_time_per_task()
            return remaining_tasks * avg_time
            
        except Exception as e:
            logger.error(f"❌ 计算预估剩余时间失败: {e}")
            return 0.0
    
    def get_total_duration(self) -> str:
        """
        获取总耗时
        
        Returns:
            耗时字符串
        """
        try:
            if not self.start_time:
                return "未开始"
            
            end_time = self.end_time or datetime.now()
            duration = end_time - self.start_time
            
            return str(duration).split('.')[0]  # 去掉微秒
            
        except Exception as e:
            logger.error(f"❌ 计算总耗时失败: {e}")
            return "计算失败"
    
    def get_progress_info(self) -> Dict[str, Any]:
        """
        获取进度信息
        
        Returns:
            进度信息字典
        """
        try:
            total_processed = self.completed_tasks + self.failed_tasks
            progress_percent = (total_processed / self.total_tasks * 100) if self.total_tasks > 0 else 0
            
            return {
                'total_tasks': self.total_tasks,
                'completed_tasks': self.completed_tasks,
                'failed_tasks': self.failed_tasks,
                'processed_tasks': total_processed,
                'progress_percent': progress_percent,
                'success_rate': self.get_success_rate(),
                'avg_time_per_task': self.get_avg_time_per_task(),
                'estimated_remaining_time': self.get_estimated_remaining_time(),
                'total_duration': self.get_total_duration(),
                'is_finished': self.end_time is not None,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'end_time': self.end_time.isoformat() if self.end_time else None
            }
            
        except Exception as e:
            logger.error(f"❌ 获取进度信息失败: {e}")
            return {}
    
    def reset(self):
        """重置跟踪器"""
        try:
            with self.lock:
                if self.progress_bar:
                    self.progress_bar.close()
                    self.progress_bar = None
                
                self.total_tasks = 0
                self.completed_tasks = 0
                self.failed_tasks = 0
                self.start_time = None
                self.end_time = None
                self.task_times.clear()
                self.last_update_time = None
                
                self.task_stats = {
                    'total': 0,
                    'completed': 0,
                    'failed': 0,
                    'success_rate': 0.0,
                    'avg_time_per_task': 0.0,
                    'estimated_remaining_time': 0.0
                }
                
                logger.info("🔄 进度跟踪器已重置")
                
        except Exception as e:
            logger.error(f"❌ 重置进度跟踪器失败: {e}")
    
    def is_active(self) -> bool:
        """
        检查是否正在跟踪
        
        Returns:
            是否活跃
        """
        return self.start_time is not None and self.end_time is None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息
        """
        return self.task_stats.copy()


class MultiTaskProgressTracker:
    """多任务进度跟踪器"""
    
    def __init__(self):
        """初始化多任务进度跟踪器"""
        self.trackers = {}
        self.lock = threading.Lock()
    
    def create_tracker(self, task_name: str, show_progress_bar: bool = True) -> ProgressTracker:
        """
        创建任务跟踪器
        
        Args:
            task_name: 任务名称
            show_progress_bar: 是否显示进度条
            
        Returns:
            进度跟踪器
        """
        try:
            with self.lock:
                tracker = ProgressTracker(show_progress_bar)
                self.trackers[task_name] = tracker
                return tracker
                
        except Exception as e:
            logger.error(f"❌ 创建任务跟踪器失败 {task_name}: {e}")
            return ProgressTracker(show_progress_bar)
    
    def get_tracker(self, task_name: str) -> Optional[ProgressTracker]:
        """
        获取任务跟踪器
        
        Args:
            task_name: 任务名称
            
        Returns:
            进度跟踪器
        """
        return self.trackers.get(task_name)
    
    def remove_tracker(self, task_name: str):
        """
        移除任务跟踪器
        
        Args:
            task_name: 任务名称
        """
        try:
            with self.lock:
                if task_name in self.trackers:
                    tracker = self.trackers[task_name]
                    tracker.finish()
                    del self.trackers[task_name]
                    
        except Exception as e:
            logger.error(f"❌ 移除任务跟踪器失败 {task_name}: {e}")
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务统计
        
        Returns:
            所有任务统计信息
        """
        try:
            with self.lock:
                return {
                    name: tracker.get_progress_info()
                    for name, tracker in self.trackers.items()
                }
                
        except Exception as e:
            logger.error(f"❌ 获取所有任务统计失败: {e}")
            return {}
    
    def cleanup(self):
        """清理所有跟踪器"""
        try:
            with self.lock:
                for tracker in self.trackers.values():
                    tracker.finish()
                
                self.trackers.clear()
                logger.info("🧹 多任务进度跟踪器已清理")
                
        except Exception as e:
            logger.error(f"❌ 清理多任务进度跟踪器失败: {e}")
