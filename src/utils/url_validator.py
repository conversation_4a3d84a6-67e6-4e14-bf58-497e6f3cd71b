#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL验证器 - 验证URL的有效性和可访问性
"""

import re
import asyncio
import aiohttp
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urlparse, urljoin
from loguru import logger
import time


class URLValidator:
    """URL验证器类"""
    
    def __init__(self, timeout: int = 10, max_retries: int = 3):
        """
        初始化URL验证器
        
        Args:
            timeout: 请求超时时间
            max_retries: 最大重试次数
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.session = None
        
        # URL模式
        self.url_patterns = {
            'boss_job': r'https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html',
            'boss_company': r'https://www\.zhipin\.com/gongsi/[a-zA-Z0-9]+\.html',
            'boss_search': r'https://www\.zhipin\.com/web/geek/job'
        }
        
        # 验证缓存
        self.validation_cache = {}
        self.cache_ttl = 3600  # 1小时缓存
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
    
    async def initialize(self):
        """初始化验证器"""
        try:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=10,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=False
            )
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            )
            
            logger.debug("✅ URL验证器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ URL验证器初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.session:
                await self.session.close()
            
            self.validation_cache.clear()
            logger.debug("✅ URL验证器资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ URL验证器清理失败: {e}")
    
    def is_valid_url(self, url: str) -> bool:
        """
        检查URL格式是否有效
        
        Args:
            url: URL字符串
            
        Returns:
            是否有效
        """
        try:
            if not url or not isinstance(url, str):
                return False
            
            # 基本URL格式检查
            url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            if not re.match(url_pattern, url):
                return False
            
            # 解析URL
            parsed = urlparse(url)
            
            # 检查必需组件
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # 检查协议
            if parsed.scheme not in ['http', 'https']:
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"URL格式检查失败 {url}: {e}")
            return False
    
    def is_valid_boss_job_url(self, url: str) -> bool:
        """
        检查是否为有效的BOSS直聘岗位URL
        
        Args:
            url: URL字符串
            
        Returns:
            是否有效
        """
        try:
            if not self.is_valid_url(url):
                return False
            
            # 检查域名
            parsed = urlparse(url)
            if parsed.netloc != 'www.zhipin.com':
                return False
            
            # 检查路径模式
            pattern = self.url_patterns['boss_job']
            if re.match(pattern, url):
                return True
            
            # 兼容其他可能的岗位URL格式
            if '/job_detail/' in url and url.endswith('.html'):
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"BOSS岗位URL检查失败 {url}: {e}")
            return False
    
    async def validate_url_accessibility(self, url: str, use_cache: bool = True) -> Tuple[bool, Dict[str, Any]]:
        """
        验证URL可访问性
        
        Args:
            url: URL字符串
            use_cache: 是否使用缓存
            
        Returns:
            (是否可访问, 详细信息)
        """
        try:
            # 检查缓存
            if use_cache and url in self.validation_cache:
                cache_data = self.validation_cache[url]
                if time.time() - cache_data['timestamp'] < self.cache_ttl:
                    return cache_data['accessible'], cache_data['info']
            
            if not self.session:
                await self.initialize()
            
            info = {
                'url': url,
                'status_code': None,
                'response_time': None,
                'content_length': None,
                'content_type': None,
                'error': None
            }
            
            start_time = time.time()
            
            for attempt in range(self.max_retries):
                try:
                    async with self.session.head(url) as response:
                        info['status_code'] = response.status
                        info['response_time'] = time.time() - start_time
                        info['content_length'] = response.headers.get('content-length')
                        info['content_type'] = response.headers.get('content-type')
                        
                        # 检查状态码
                        if response.status == 200:
                            accessible = True
                        elif response.status in [301, 302, 303, 307, 308]:
                            # 重定向，尝试跟随
                            location = response.headers.get('location')
                            if location:
                                redirect_url = urljoin(url, location)
                                return await self.validate_url_accessibility(redirect_url, use_cache=False)
                            accessible = False
                        else:
                            accessible = False
                        
                        break
                        
                except aiohttp.ClientError as e:
                    info['error'] = str(e)
                    if attempt == self.max_retries - 1:
                        accessible = False
                    else:
                        await asyncio.sleep(1)  # 重试前等待
                        continue
            else:
                accessible = False
            
            # 缓存结果
            if use_cache:
                self.validation_cache[url] = {
                    'accessible': accessible,
                    'info': info,
                    'timestamp': time.time()
                }
            
            return accessible, info
            
        except Exception as e:
            logger.error(f"❌ 验证URL可访问性失败 {url}: {e}")
            return False, {'url': url, 'error': str(e)}
    
    async def validate_urls_batch(self, urls: List[str], max_concurrent: int = 10) -> Dict[str, Tuple[bool, Dict[str, Any]]]:
        """
        批量验证URL
        
        Args:
            urls: URL列表
            max_concurrent: 最大并发数
            
        Returns:
            验证结果字典
        """
        try:
            if not self.session:
                await self.initialize()
            
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def validate_single(url):
                async with semaphore:
                    return url, await self.validate_url_accessibility(url)
            
            tasks = [validate_single(url) for url in urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            validation_results = {}
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"❌ 批量验证出错: {result}")
                    continue
                
                url, (accessible, info) = result
                validation_results[url] = (accessible, info)
            
            return validation_results
            
        except Exception as e:
            logger.error(f"❌ 批量验证URL失败: {e}")
            return {}
    
    async def filter_valid_urls(self, urls: List[str], check_accessibility: bool = True) -> List[str]:
        """
        过滤有效的URL
        
        Args:
            urls: URL列表
            check_accessibility: 是否检查可访问性
            
        Returns:
            有效URL列表
        """
        try:
            valid_urls = []
            
            # 格式检查
            format_valid_urls = [url for url in urls if self.is_valid_url(url)]
            
            if not check_accessibility:
                return format_valid_urls
            
            # 可访问性检查
            if format_valid_urls:
                validation_results = await self.validate_urls_batch(format_valid_urls)
                
                for url in format_valid_urls:
                    if url in validation_results:
                        accessible, _ = validation_results[url]
                        if accessible:
                            valid_urls.append(url)
            
            return valid_urls
            
        except Exception as e:
            logger.error(f"❌ 过滤有效URL失败: {e}")
            return []
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """
        获取验证统计信息
        
        Returns:
            统计信息
        """
        try:
            cache_size = len(self.validation_cache)
            
            # 统计缓存中的成功/失败数量
            successful = sum(1 for data in self.validation_cache.values() if data['accessible'])
            failed = cache_size - successful
            
            return {
                'cache_size': cache_size,
                'successful_validations': successful,
                'failed_validations': failed,
                'cache_hit_rate': successful / cache_size if cache_size > 0 else 0,
                'timeout': self.timeout,
                'max_retries': self.max_retries
            }
            
        except Exception as e:
            logger.error(f"❌ 获取验证统计失败: {e}")
            return {}
    
    def clear_cache(self):
        """清理验证缓存"""
        try:
            self.validation_cache.clear()
            logger.info("🧹 URL验证缓存已清理")
        except Exception as e:
            logger.error(f"❌ 清理验证缓存失败: {e}")
    
    def extract_urls_from_text(self, text: str) -> List[str]:
        """
        从文本中提取URL
        
        Args:
            text: 文本内容
            
        Returns:
            URL列表
        """
        try:
            url_pattern = r'https?://[^\s<>"\']+[^\s<>"\'.,;!?]'
            urls = re.findall(url_pattern, text)
            
            # 去重并过滤
            unique_urls = list(set(urls))
            valid_urls = [url for url in unique_urls if self.is_valid_url(url)]
            
            return valid_urls
            
        except Exception as e:
            logger.error(f"❌ 从文本提取URL失败: {e}")
            return []
    
    def normalize_url(self, url: str, base_url: str = None) -> str:
        """
        标准化URL
        
        Args:
            url: 原始URL
            base_url: 基础URL
            
        Returns:
            标准化后的URL
        """
        try:
            if not url:
                return ""
            
            # 如果是相对URL，转换为绝对URL
            if base_url and not url.startswith(('http://', 'https://')):
                url = urljoin(base_url, url)
            
            # 解析并重构URL
            parsed = urlparse(url)
            
            # 移除fragment
            normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            
            if parsed.query:
                normalized += f"?{parsed.query}"
            
            return normalized
            
        except Exception as e:
            logger.error(f"❌ 标准化URL失败 {url}: {e}")
            return url
