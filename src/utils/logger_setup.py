#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志设置模块 - 配置系统日志
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any
from loguru import logger
from datetime import datetime


def setup_logger(config: Dict[str, Any]):
    """
    设置日志系统
    
    Args:
        config: 配置字典
    """
    try:
        # 移除默认处理器
        logger.remove()
        
        # 获取日志配置
        log_level = config.get('app', {}).get('log_level', 'INFO')
        debug_mode = config.get('app', {}).get('debug', False)
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 控制台日志格式
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
        
        # 文件日志格式
        file_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{message}"
        )
        
        # 添加控制台处理器
        logger.add(
            sys.stdout,
            format=console_format,
            level=log_level,
            colorize=True,
            backtrace=debug_mode,
            diagnose=debug_mode
        )
        
        # 添加文件处理器 - 所有日志
        current_date = datetime.now().strftime('%Y%m%d')
        logger.add(
            log_dir / f"boss_crawler_{current_date}.log",
            format=file_format,
            level="DEBUG",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
        
        # 添加错误日志文件
        logger.add(
            log_dir / f"boss_crawler_error_{current_date}.log",
            format=file_format,
            level="ERROR",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            backtrace=True,
            diagnose=True
        )
        
        # 如果是调试模式，添加详细日志
        if debug_mode:
            logger.add(
                log_dir / f"boss_crawler_debug_{current_date}.log",
                format=file_format,
                level="DEBUG",
                rotation="100 MB",
                retention="7 days",
                compression="zip",
                encoding="utf-8",
                backtrace=True,
                diagnose=True
            )
        
        # 设置第三方库日志级别
        _configure_third_party_loggers(debug_mode)
        
        logger.info("✅ 日志系统初始化完成")
        logger.info(f"📁 日志目录: {log_dir.absolute()}")
        logger.info(f"📊 日志级别: {log_level}")
        
    except Exception as e:
        print(f"❌ 日志系统初始化失败: {e}")
        raise


def _configure_third_party_loggers(debug_mode: bool):
    """
    配置第三方库日志
    
    Args:
        debug_mode: 是否为调试模式
    """
    import logging
    
    # 设置第三方库日志级别
    third_party_loggers = [
        'urllib3',
        'requests',
        'aiohttp',
        'selenium',
        'playwright',
        'openai',
        'httpx'
    ]
    
    log_level = logging.DEBUG if debug_mode else logging.WARNING
    
    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(log_level)
    
    # 特殊处理一些库
    logging.getLogger('urllib3.connectionpool').setLevel(logging.ERROR)
    logging.getLogger('selenium.webdriver.remote.remote_connection').setLevel(logging.WARNING)


def get_logger_stats() -> Dict[str, Any]:
    """
    获取日志统计信息
    
    Returns:
        日志统计信息
    """
    try:
        log_dir = Path("logs")
        
        if not log_dir.exists():
            return {"status": "not_initialized"}
        
        log_files = list(log_dir.glob("*.log"))
        total_size = sum(f.stat().st_size for f in log_files if f.exists())
        
        return {
            "status": "active",
            "log_directory": str(log_dir.absolute()),
            "log_files_count": len(log_files),
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "log_files": [f.name for f in log_files]
        }
        
    except Exception as e:
        return {"status": "error", "error": str(e)}


def cleanup_old_logs(days: int = 30):
    """
    清理旧日志文件
    
    Args:
        days: 保留天数
    """
    try:
        log_dir = Path("logs")
        
        if not log_dir.exists():
            return
        
        import time
        current_time = time.time()
        cutoff_time = current_time - (days * 24 * 60 * 60)
        
        removed_count = 0
        removed_size = 0
        
        for log_file in log_dir.glob("*.log*"):
            if log_file.stat().st_mtime < cutoff_time:
                file_size = log_file.stat().st_size
                log_file.unlink()
                removed_count += 1
                removed_size += file_size
        
        if removed_count > 0:
            logger.info(f"🧹 清理了 {removed_count} 个旧日志文件，释放空间 {removed_size / (1024 * 1024):.2f} MB")
        
    except Exception as e:
        logger.error(f"❌ 清理旧日志失败: {e}")


class LoggerContextManager:
    """日志上下文管理器"""
    
    def __init__(self, context_name: str):
        """
        初始化日志上下文管理器
        
        Args:
            context_name: 上下文名称
        """
        self.context_name = context_name
        self.start_time = None
    
    def __enter__(self):
        """进入上下文"""
        self.start_time = datetime.now()
        logger.info(f"🚀 开始 {self.context_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        if exc_type is None:
            logger.success(f"✅ 完成 {self.context_name}，耗时: {duration}")
        else:
            logger.error(f"❌ {self.context_name} 失败，耗时: {duration}，错误: {exc_val}")
        
        return False  # 不抑制异常


def log_function_call(func_name: str = None):
    """
    函数调用日志装饰器
    
    Args:
        func_name: 函数名称（可选）
    """
    def decorator(func):
        import functools
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            name = func_name or func.__name__
            start_time = datetime.now()
            
            logger.debug(f"📞 调用函数: {name}")
            
            try:
                result = await func(*args, **kwargs)
                duration = datetime.now() - start_time
                logger.debug(f"✅ 函数 {name} 完成，耗时: {duration}")
                return result
            except Exception as e:
                duration = datetime.now() - start_time
                logger.error(f"❌ 函数 {name} 失败，耗时: {duration}，错误: {e}")
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            name = func_name or func.__name__
            start_time = datetime.now()
            
            logger.debug(f"📞 调用函数: {name}")
            
            try:
                result = func(*args, **kwargs)
                duration = datetime.now() - start_time
                logger.debug(f"✅ 函数 {name} 完成，耗时: {duration}")
                return result
            except Exception as e:
                duration = datetime.now() - start_time
                logger.error(f"❌ 函数 {name} 失败，耗时: {duration}，错误: {e}")
                raise
        
        # 检查是否为异步函数
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
