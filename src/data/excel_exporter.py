#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel导出器 - 将数据导出为Excel格式
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows


class ExcelExporter:
    """Excel导出器类"""
    
    def __init__(self, excel_config: Dict[str, Any]):
        """
        初始化Excel导出器
        
        Args:
            excel_config: Excel配置
        """
        self.config = excel_config
        self.output_dir = Path("output")
        self.output_dir.mkdir(exist_ok=True)
        
        self.current_file = None
        self.workbook = None
        self.worksheet = None
        
        # 列映射
        self.column_mapping = {
            'job_title': '岗位名称',
            'salary_info': '薪资情况',
            'benefits': '福利待遇',
            'experience_req': '工作经验要求',
            'education_req': '学历要求',
            'job_description': '职位详细要求',
            'company_info': '公司介绍',
            'work_location': '工作地址',
            'job_url': '岗位详情页URL',
            'company_name': '公司名称',
            'salary_min': '最低薪资(K)',
            'salary_max': '最高薪资(K)',
            'salary_months': '薪资月数',
            'experience_min': '最低经验(年)',
            'experience_max': '最高经验(年)',
            'education_level': '学历等级',
            'company_size_category': '公司规模',
            'crawl_time': '爬取时间',
            'processed_time': '处理时间'
        }
    
    async def export_jobs(self, jobs_data: List[Dict[str, Any]]) -> str:
        """
        导出岗位数据到Excel
        
        Args:
            jobs_data: 岗位数据列表
            
        Returns:
            输出文件路径
        """
        try:
            logger.info(f"📊 开始导出 {len(jobs_data)} 条岗位数据到Excel...")
            
            if not jobs_data:
                logger.warning("⚠️ 没有数据需要导出")
                return ""
            
            # 生成文件名
            filename = self._generate_filename()
            filepath = self.output_dir / filename
            
            # 创建DataFrame
            df = self._create_dataframe(jobs_data)
            
            # 导出到Excel
            await self._export_to_excel(df, filepath)
            
            self.current_file = str(filepath)
            logger.success(f"✅ 数据已导出到: {filepath}")
            
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ 导出Excel失败: {e}")
            raise
    
    def _generate_filename(self) -> str:
        """
        生成文件名
        
        Returns:
            文件名
        """
        try:
            filename_template = self.config.get('filename', 'boss_jobs_{date}.xlsx')
            
            # 替换日期占位符
            current_date = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = filename_template.format(date=current_date)
            
            return filename
            
        except Exception as e:
            logger.error(f"❌ 生成文件名失败: {e}")
            return f"boss_jobs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    def _create_dataframe(self, jobs_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        创建DataFrame
        
        Args:
            jobs_data: 岗位数据列表
            
        Returns:
            DataFrame对象
        """
        try:
            # 确定要导出的列
            export_columns = self._get_export_columns(jobs_data)
            
            # 创建DataFrame
            df_data = []
            for job in jobs_data:
                row_data = {}
                for col in export_columns:
                    row_data[self.column_mapping.get(col, col)] = job.get(col, '')
                df_data.append(row_data)
            
            df = pd.DataFrame(df_data)
            
            # 数据类型转换和清理
            df = self._clean_dataframe(df)
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 创建DataFrame失败: {e}")
            raise
    
    def _get_export_columns(self, jobs_data: List[Dict[str, Any]]) -> List[str]:
        """
        获取要导出的列
        
        Args:
            jobs_data: 岗位数据列表
            
        Returns:
            列名列表
        """
        try:
            # 基础必需列
            base_columns = [
                'job_title', 'salary_info', 'benefits', 'experience_req',
                'education_req', 'job_description', 'company_info',
                'work_location', 'job_url'
            ]
            
            # 从数据中获取所有可用列
            all_columns = set()
            for job in jobs_data[:10]:  # 只检查前10条数据
                all_columns.update(job.keys())
            
            # 确定最终导出列
            export_columns = []
            
            # 添加基础列
            for col in base_columns:
                if col in all_columns:
                    export_columns.append(col)
            
            # 添加额外的有用列
            extra_columns = [
                'company_name', 'salary_min', 'salary_max', 'salary_months',
                'experience_min', 'experience_max', 'education_level',
                'company_size_category', 'crawl_time'
            ]
            
            for col in extra_columns:
                if col in all_columns and col not in export_columns:
                    export_columns.append(col)
            
            return export_columns
            
        except Exception as e:
            logger.error(f"❌ 获取导出列失败: {e}")
            return ['job_title', 'salary_info', 'company_info', 'job_url']
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理DataFrame
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清理后的DataFrame
        """
        try:
            # 处理空值
            df = df.fillna('')
            
            # 处理时间列
            time_columns = ['爬取时间', '处理时间']
            for col in time_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
                    df[col] = df[col].fillna('')
            
            # 处理数值列
            numeric_columns = ['最低薪资(K)', '最高薪资(K)', '薪资月数', '最低经验(年)', '最高经验(年)']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0).astype(int)
            
            # 限制文本长度
            text_columns = ['职位详细要求', '公司介绍']
            for col in text_columns:
                if col in df.columns:
                    df[col] = df[col].astype(str).str[:1000]  # 限制1000字符
            
            return df
            
        except Exception as e:
            logger.error(f"❌ 清理DataFrame失败: {e}")
            return df
    
    async def _export_to_excel(self, df: pd.DataFrame, filepath: Path):
        """
        导出DataFrame到Excel文件
        
        Args:
            df: DataFrame对象
            filepath: 输出文件路径
        """
        try:
            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = self.config.get('sheet_name', '招聘信息')
            
            # 写入数据
            for r in dataframe_to_rows(df, index=False, header=True):
                worksheet.append(r)
            
            # 设置样式
            self._apply_excel_styles(worksheet, df)
            
            # 保存文件
            workbook.save(filepath)
            workbook.close()
            
        except Exception as e:
            logger.error(f"❌ 导出到Excel文件失败: {e}")
            raise
    
    def _apply_excel_styles(self, worksheet, df: pd.DataFrame):
        """
        应用Excel样式
        
        Args:
            worksheet: 工作表对象
            df: DataFrame对象
        """
        try:
            # 定义样式
            header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
            header_alignment = Alignment(horizontal='center', vertical='center')
            
            data_font = Font(name='微软雅黑', size=10)
            data_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
            
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 应用标题行样式
            for col_num in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border
            
            # 应用数据行样式
            for row_num in range(2, len(df) + 2):
                for col_num in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    cell.font = data_font
                    cell.alignment = data_alignment
                    cell.border = border
            
            # 设置列宽
            self._adjust_column_widths(worksheet, df)
            
            # 冻结首行
            worksheet.freeze_panes = 'A2'
            
        except Exception as e:
            logger.error(f"❌ 应用Excel样式失败: {e}")
    
    def _adjust_column_widths(self, worksheet, df: pd.DataFrame):
        """
        调整列宽
        
        Args:
            worksheet: 工作表对象
            df: DataFrame对象
        """
        try:
            # 列宽映射
            width_mapping = {
                '岗位名称': 20,
                '薪资情况': 15,
                '福利待遇': 30,
                '工作经验要求': 15,
                '学历要求': 12,
                '职位详细要求': 50,
                '公司介绍': 40,
                '工作地址': 25,
                '岗位详情页URL': 40,
                '公司名称': 20,
                '最低薪资(K)': 12,
                '最高薪资(K)': 12,
                '薪资月数': 10,
                '最低经验(年)': 12,
                '最高经验(年)': 12,
                '学历等级': 12,
                '公司规模': 12,
                '爬取时间': 18,
                '处理时间': 18
            }
            
            for col_num, column_name in enumerate(df.columns, 1):
                width = width_mapping.get(column_name, 15)
                worksheet.column_dimensions[openpyxl.utils.get_column_letter(col_num)].width = width
            
        except Exception as e:
            logger.error(f"❌ 调整列宽失败: {e}")
    
    def get_output_file(self) -> Optional[str]:
        """
        获取输出文件路径
        
        Returns:
            输出文件路径
        """
        return self.current_file
    
    def get_export_stats(self) -> Dict[str, Any]:
        """
        获取导出统计信息
        
        Returns:
            统计信息
        """
        stats = {
            'output_directory': str(self.output_dir),
            'current_file': self.current_file,
            'column_count': len(self.column_mapping),
            'auto_save_interval': self.config.get('auto_save_interval', 50)
        }
        
        if self.current_file and os.path.exists(self.current_file):
            stats['file_size'] = os.path.getsize(self.current_file)
            stats['file_modified'] = datetime.fromtimestamp(
                os.path.getmtime(self.current_file)
            ).isoformat()
        
        return stats
    
    async def append_jobs(self, jobs_data: List[Dict[str, Any]]) -> str:
        """
        追加岗位数据到现有Excel文件
        
        Args:
            jobs_data: 岗位数据列表
            
        Returns:
            输出文件路径
        """
        try:
            if not self.current_file or not os.path.exists(self.current_file):
                return await self.export_jobs(jobs_data)
            
            logger.info(f"📊 追加 {len(jobs_data)} 条岗位数据到Excel...")
            
            # 读取现有数据
            existing_df = pd.read_excel(self.current_file)
            
            # 创建新数据DataFrame
            new_df = self._create_dataframe(jobs_data)
            
            # 合并数据
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            
            # 去重
            combined_df = combined_df.drop_duplicates(subset=['岗位详情页URL'], keep='last')
            
            # 重新导出
            await self._export_to_excel(combined_df, Path(self.current_file))
            
            logger.success(f"✅ 数据已追加到: {self.current_file}")
            
            return self.current_file
            
        except Exception as e:
            logger.error(f"❌ 追加Excel数据失败: {e}")
            return await self.export_jobs(jobs_data)
