#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器 - 处理和验证爬取的数据
"""

import re
from typing import Dict, Any, Optional, List
from loguru import logger
from datetime import datetime
import json


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self, extraction_config: Dict[str, Any]):
        """
        初始化数据处理器
        
        Args:
            extraction_config: 数据提取配置
        """
        self.config = extraction_config
        self.required_fields = extraction_config.get('required_fields', [])
        self.validation_config = extraction_config.get('validation', {})
        self.filters_config = extraction_config.get('filters', {})
    
    def validate_job_data(self, job_data: Dict[str, Any]) -> bool:
        """
        验证岗位数据
        
        Args:
            job_data: 岗位数据
            
        Returns:
            是否有效
        """
        try:
            # 检查必需字段
            for field in self.required_fields:
                if field not in job_data or not job_data[field]:
                    logger.debug(f"缺少必需字段: {field}")
                    return False
            
            # 验证薪资范围
            if not self._validate_salary(job_data.get('salary_info', '')):
                logger.debug("薪资验证失败")
                return False
            
            # 验证URL
            if self.validation_config.get('url_validation', True):
                if not self._validate_url(job_data.get('job_url', '')):
                    logger.debug("URL验证失败")
                    return False
            
            # 过滤检查
            if not self._check_filters(job_data):
                logger.debug("过滤检查失败")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 验证岗位数据失败: {e}")
            return False
    
    def _validate_salary(self, salary_info: str) -> bool:
        """
        验证薪资信息
        
        Args:
            salary_info: 薪资信息字符串
            
        Returns:
            是否有效
        """
        try:
            if not salary_info:
                return False
            
            # 提取薪资数字
            salary_numbers = re.findall(r'(\d+)', salary_info)
            
            if not salary_numbers:
                return False
            
            # 获取最低薪资
            min_salary = int(salary_numbers[0])
            
            # 检查薪资范围
            min_limit = self.validation_config.get('min_salary', 0)
            max_limit = self.validation_config.get('max_salary', 1000)
            
            return min_limit <= min_salary <= max_limit
            
        except Exception as e:
            logger.debug(f"薪资验证错误: {e}")
            return False
    
    def _validate_url(self, url: str) -> bool:
        """
        验证URL
        
        Args:
            url: URL字符串
            
        Returns:
            是否有效
        """
        try:
            if not url:
                return False
            
            # 检查URL格式
            url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            if not re.match(url_pattern, url):
                return False
            
            # 检查是否为BOSS直聘URL
            if 'zhipin.com' not in url:
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"URL验证错误: {e}")
            return False
    
    def _check_filters(self, job_data: Dict[str, Any]) -> bool:
        """
        检查过滤条件
        
        Args:
            job_data: 岗位数据
            
        Returns:
            是否通过过滤
        """
        try:
            # 检查排除关键词
            exclude_keywords = self.filters_config.get('exclude_keywords', [])
            job_title = job_data.get('job_title', '').lower()
            job_desc = job_data.get('job_description', '').lower()
            
            for keyword in exclude_keywords:
                if keyword.lower() in job_title or keyword.lower() in job_desc:
                    logger.debug(f"包含排除关键词: {keyword}")
                    return False
            
            # 检查排除公司
            exclude_companies = self.filters_config.get('exclude_companies', [])
            company_name = job_data.get('company_name', '').lower()
            
            for company in exclude_companies:
                if company.lower() in company_name:
                    logger.debug(f"包含排除公司: {company}")
                    return False
            
            # 检查最小公司规模
            min_company_size = self.filters_config.get('min_company_size', 0)
            if min_company_size > 0:
                company_info = job_data.get('company_info', '')
                if not self._check_company_size(company_info, min_company_size):
                    logger.debug(f"公司规模不符合要求: {min_company_size}")
                    return False
            
            return True
            
        except Exception as e:
            logger.debug(f"过滤检查错误: {e}")
            return True  # 出错时默认通过
    
    def _check_company_size(self, company_info: str, min_size: int) -> bool:
        """
        检查公司规模
        
        Args:
            company_info: 公司信息
            min_size: 最小规模
            
        Returns:
            是否符合要求
        """
        try:
            # 提取公司规模数字
            size_patterns = [
                r'(\d+)-(\d+)人',
                r'(\d+)人以上',
                r'(\d+)\+人'
            ]
            
            for pattern in size_patterns:
                matches = re.findall(pattern, company_info)
                if matches:
                    if isinstance(matches[0], tuple):
                        # 范围格式
                        min_employees = int(matches[0][0])
                    else:
                        # 单个数字
                        min_employees = int(matches[0])
                    
                    return min_employees >= min_size
            
            return True  # 无法确定规模时默认通过
            
        except Exception as e:
            logger.debug(f"公司规模检查错误: {e}")
            return True
    
    def process_job_data(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理岗位数据
        
        Args:
            job_data: 原始岗位数据
            
        Returns:
            处理后的岗位数据
        """
        try:
            processed_data = job_data.copy()
            
            # 清洗和标准化数据
            processed_data = self._clean_data(processed_data)
            
            # 提取结构化信息
            processed_data = self._extract_structured_info(processed_data)
            
            # 添加处理时间戳
            processed_data['processed_time'] = datetime.now().isoformat()
            
            return processed_data
            
        except Exception as e:
            logger.error(f"❌ 处理岗位数据失败: {e}")
            return job_data
    
    def _clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗数据
        
        Args:
            data: 原始数据
            
        Returns:
            清洗后的数据
        """
        try:
            cleaned_data = {}
            
            for key, value in data.items():
                if isinstance(value, str):
                    # 去除多余空白字符
                    cleaned_value = re.sub(r'\s+', ' ', value.strip())
                    # 去除特殊字符
                    cleaned_value = re.sub(r'[^\w\s\-.,，。：:；;()（）【】\[\]/\\]', '', cleaned_value)
                    cleaned_data[key] = cleaned_value
                else:
                    cleaned_data[key] = value
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"❌ 清洗数据失败: {e}")
            return data
    
    def _extract_structured_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取结构化信息
        
        Args:
            data: 数据
            
        Returns:
            包含结构化信息的数据
        """
        try:
            # 提取薪资结构化信息
            salary_info = self._parse_salary(data.get('salary_info', ''))
            data.update(salary_info)
            
            # 提取工作经验结构化信息
            experience_info = self._parse_experience(data.get('experience_req', ''))
            data.update(experience_info)
            
            # 提取学历结构化信息
            education_info = self._parse_education(data.get('education_req', ''))
            data.update(education_info)
            
            # 提取公司规模结构化信息
            company_size_info = self._parse_company_size(data.get('company_info', ''))
            data.update(company_size_info)
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 提取结构化信息失败: {e}")
            return data
    
    def _parse_salary(self, salary_str: str) -> Dict[str, Any]:
        """
        解析薪资信息
        
        Args:
            salary_str: 薪资字符串
            
        Returns:
            结构化薪资信息
        """
        try:
            result = {
                'salary_min': None,
                'salary_max': None,
                'salary_months': 12,
                'salary_unit': 'K'
            }
            
            if not salary_str:
                return result
            
            # 提取薪资范围
            salary_pattern = r'(\d+)-(\d+)([KkWw万千])'
            match = re.search(salary_pattern, salary_str)
            
            if match:
                result['salary_min'] = int(match.group(1))
                result['salary_max'] = int(match.group(2))
                result['salary_unit'] = match.group(3).upper()
            
            # 提取薪资月数
            months_pattern = r'(\d+)薪'
            months_match = re.search(months_pattern, salary_str)
            
            if months_match:
                result['salary_months'] = int(months_match.group(1))
            
            return result
            
        except Exception as e:
            logger.debug(f"解析薪资信息错误: {e}")
            return {}
    
    def _parse_experience(self, experience_str: str) -> Dict[str, Any]:
        """
        解析工作经验
        
        Args:
            experience_str: 经验字符串
            
        Returns:
            结构化经验信息
        """
        try:
            result = {
                'experience_min': None,
                'experience_max': None,
                'experience_type': experience_str
            }
            
            if not experience_str:
                return result
            
            # 提取经验年限
            exp_pattern = r'(\d+)-(\d+)年'
            match = re.search(exp_pattern, experience_str)
            
            if match:
                result['experience_min'] = int(match.group(1))
                result['experience_max'] = int(match.group(2))
            else:
                # 单个年限
                single_pattern = r'(\d+)年'
                single_match = re.search(single_pattern, experience_str)
                if single_match:
                    years = int(single_match.group(1))
                    result['experience_min'] = years
                    result['experience_max'] = years
            
            return result
            
        except Exception as e:
            logger.debug(f"解析工作经验错误: {e}")
            return {}
    
    def _parse_education(self, education_str: str) -> Dict[str, Any]:
        """
        解析学历要求
        
        Args:
            education_str: 学历字符串
            
        Returns:
            结构化学历信息
        """
        try:
            result = {
                'education_level': education_str,
                'education_score': 0
            }
            
            if not education_str:
                return result
            
            # 学历等级映射
            education_mapping = {
                '博士': 7,
                '硕士': 6,
                '本科': 5,
                '大专': 4,
                '高中': 3,
                '中专': 2,
                '初中': 1
            }
            
            for edu, score in education_mapping.items():
                if edu in education_str:
                    result['education_score'] = score
                    break
            
            return result
            
        except Exception as e:
            logger.debug(f"解析学历要求错误: {e}")
            return {}
    
    def _parse_company_size(self, company_info: str) -> Dict[str, Any]:
        """
        解析公司规模
        
        Args:
            company_info: 公司信息
            
        Returns:
            结构化公司规模信息
        """
        try:
            result = {
                'company_size_min': None,
                'company_size_max': None,
                'company_size_category': ''
            }
            
            if not company_info:
                return result
            
            # 提取公司规模
            size_patterns = [
                (r'(\d+)-(\d+)人', 'range'),
                (r'(\d+)人以上', 'above'),
                (r'(\d+)\+人', 'above')
            ]
            
            for pattern, pattern_type in size_patterns:
                match = re.search(pattern, company_info)
                if match:
                    if pattern_type == 'range':
                        result['company_size_min'] = int(match.group(1))
                        result['company_size_max'] = int(match.group(2))
                    elif pattern_type == 'above':
                        result['company_size_min'] = int(match.group(1))
                        result['company_size_max'] = None
                    break
            
            # 分类公司规模
            if result['company_size_min']:
                min_size = result['company_size_min']
                if min_size < 50:
                    result['company_size_category'] = '小型'
                elif min_size < 500:
                    result['company_size_category'] = '中型'
                else:
                    result['company_size_category'] = '大型'
            
            return result
            
        except Exception as e:
            logger.debug(f"解析公司规模错误: {e}")
            return {}
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            统计信息
        """
        return {
            'required_fields_count': len(self.required_fields),
            'validation_enabled': bool(self.validation_config),
            'filters_enabled': bool(self.filters_config),
            'exclude_keywords_count': len(self.filters_config.get('exclude_keywords', [])),
            'exclude_companies_count': len(self.filters_config.get('exclude_companies', []))
        }
