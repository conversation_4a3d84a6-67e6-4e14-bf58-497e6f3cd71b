#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理管理器 - 管理代理服务器和IP轮换
"""

import asyncio
import aiohttp
import random
from typing import Dict, List, Optional, Any
from loguru import logger
from urllib.parse import urlparse
import time


class ProxyManager:
    """代理管理器类"""
    
    def __init__(self, proxy_config: Dict[str, Any]):
        """
        初始化代理管理器
        
        Args:
            proxy_config: 代理配置
        """
        self.config = proxy_config
        self.current_proxy = None
        self.proxy_list = []
        self.failed_proxies = set()
        self.last_check_time = 0
        self.check_interval = 300  # 5分钟检查一次
        
        # 从配置中获取代理
        self._load_proxies_from_config()
    
    def _load_proxies_from_config(self):
        """从配置中加载代理"""
        try:
            # 本地代理
            if self.config.get('http_proxy'):
                self.proxy_list.append({
                    'http': self.config['http_proxy'],
                    'https': self.config.get('https_proxy', self.config['http_proxy']),
                    'type': 'local'
                })
            
            # 可以扩展支持代理池
            if self.config.get('proxy_pool'):
                for proxy in self.config['proxy_pool']:
                    self.proxy_list.append({
                        'http': proxy,
                        'https': proxy,
                        'type': 'pool'
                    })
            
            logger.info(f"✅ 加载了 {len(self.proxy_list)} 个代理")
            
        except Exception as e:
            logger.error(f"❌ 加载代理配置失败: {e}")
    
    async def initialize(self):
        """初始化代理管理器"""
        try:
            logger.info("🔧 初始化代理管理器...")
            
            if not self.proxy_list:
                logger.warning("⚠️ 未配置任何代理")
                return
            
            # 测试代理可用性
            await self._test_proxies()
            
            # 选择第一个可用代理
            if self.proxy_list:
                self.current_proxy = self.proxy_list[0]
                logger.info(f"✅ 当前代理: {self.current_proxy['http']}")
            
            logger.success("✅ 代理管理器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 代理管理器初始化失败: {e}")
            raise
    
    async def _test_proxies(self):
        """测试代理可用性"""
        try:
            logger.info("🔍 测试代理可用性...")
            
            valid_proxies = []
            test_url = "https://httpbin.org/ip"
            
            for proxy in self.proxy_list:
                try:
                    if await self._test_single_proxy(proxy, test_url):
                        valid_proxies.append(proxy)
                        logger.info(f"✅ 代理可用: {proxy['http']}")
                    else:
                        logger.warning(f"⚠️ 代理不可用: {proxy['http']}")
                        
                except Exception as e:
                    logger.error(f"❌ 测试代理失败 {proxy['http']}: {e}")
            
            self.proxy_list = valid_proxies
            logger.info(f"✅ 可用代理数量: {len(self.proxy_list)}")
            
        except Exception as e:
            logger.error(f"❌ 测试代理失败: {e}")
    
    async def _test_single_proxy(self, proxy: Dict[str, str], test_url: str, timeout: int = 10) -> bool:
        """
        测试单个代理
        
        Args:
            proxy: 代理配置
            test_url: 测试URL
            timeout: 超时时间
            
        Returns:
            是否可用
        """
        try:
            connector = aiohttp.TCPConnector(ssl=False)
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout_config
            ) as session:
                async with session.get(
                    test_url,
                    proxy=proxy['http'],
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.debug(f"代理IP: {data.get('origin', 'Unknown')}")
                        return True
                    else:
                        return False
                        
        except Exception as e:
            logger.debug(f"代理测试失败 {proxy['http']}: {e}")
            return False
    
    def get_current_proxy(self) -> Optional[str]:
        """获取当前代理"""
        if self.current_proxy:
            return self.current_proxy['http']
        return None
    
    def get_proxy_dict(self) -> Dict[str, str]:
        """获取代理字典格式"""
        if self.current_proxy:
            return {
                'http': self.current_proxy['http'],
                'https': self.current_proxy['https']
            }
        return {}
    
    async def rotate_proxy(self):
        """轮换代理"""
        try:
            if len(self.proxy_list) <= 1:
                logger.warning("⚠️ 可用代理不足，无法轮换")
                return
            
            # 移除当前代理
            if self.current_proxy in self.proxy_list:
                self.proxy_list.remove(self.current_proxy)
            
            # 选择新代理
            if self.proxy_list:
                self.current_proxy = random.choice(self.proxy_list)
                logger.info(f"🔄 切换到新代理: {self.current_proxy['http']}")
            else:
                logger.error("❌ 没有可用的代理")
                self.current_proxy = None
                
        except Exception as e:
            logger.error(f"❌ 轮换代理失败: {e}")
    
    async def mark_proxy_failed(self, proxy_url: str):
        """标记代理失败"""
        try:
            self.failed_proxies.add(proxy_url)
            
            # 从可用列表中移除
            self.proxy_list = [p for p in self.proxy_list if p['http'] != proxy_url]
            
            # 如果当前代理失败，切换到新代理
            if self.current_proxy and self.current_proxy['http'] == proxy_url:
                await self.rotate_proxy()
            
            logger.warning(f"⚠️ 代理已标记为失败: {proxy_url}")
            
        except Exception as e:
            logger.error(f"❌ 标记代理失败时出错: {e}")
    
    async def check_proxy_health(self):
        """检查代理健康状态"""
        try:
            current_time = time.time()
            
            # 检查是否需要健康检查
            if current_time - self.last_check_time < self.check_interval:
                return
            
            logger.info("🔍 开始代理健康检查...")
            
            if self.current_proxy:
                test_url = "https://httpbin.org/ip"
                is_healthy = await self._test_single_proxy(self.current_proxy, test_url)
                
                if not is_healthy:
                    logger.warning(f"⚠️ 当前代理不健康: {self.current_proxy['http']}")
                    await self.mark_proxy_failed(self.current_proxy['http'])
                else:
                    logger.info("✅ 当前代理健康")
            
            self.last_check_time = current_time
            
        except Exception as e:
            logger.error(f"❌ 代理健康检查失败: {e}")
    
    async def get_proxy_for_request(self) -> Optional[Dict[str, str]]:
        """获取用于请求的代理"""
        try:
            # 检查代理健康状态
            await self.check_proxy_health()
            
            # 返回当前代理
            return self.get_proxy_dict()
            
        except Exception as e:
            logger.error(f"❌ 获取请求代理失败: {e}")
            return {}
    
    def get_proxy_stats(self) -> Dict[str, Any]:
        """获取代理统计信息"""
        return {
            'total_proxies': len(self.proxy_list),
            'failed_proxies': len(self.failed_proxies),
            'current_proxy': self.current_proxy['http'] if self.current_proxy else None,
            'last_check_time': self.last_check_time
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 清理代理管理器资源...")
            
            # 清理状态
            self.current_proxy = None
            self.proxy_list.clear()
            self.failed_proxies.clear()
            
            logger.info("✅ 代理管理器资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理代理管理器资源失败: {e}")


class ProxyRotator:
    """代理轮换器 - 用于自动轮换代理"""
    
    def __init__(self, proxy_manager: ProxyManager, rotation_interval: int = 600):
        """
        初始化代理轮换器
        
        Args:
            proxy_manager: 代理管理器
            rotation_interval: 轮换间隔（秒）
        """
        self.proxy_manager = proxy_manager
        self.rotation_interval = rotation_interval
        self.is_running = False
        self.rotation_task = None
    
    async def start(self):
        """开始自动轮换"""
        try:
            if self.is_running:
                return
            
            self.is_running = True
            self.rotation_task = asyncio.create_task(self._rotation_loop())
            logger.info(f"🔄 代理自动轮换已启动，间隔: {self.rotation_interval}秒")
            
        except Exception as e:
            logger.error(f"❌ 启动代理轮换失败: {e}")
    
    async def stop(self):
        """停止自动轮换"""
        try:
            self.is_running = False
            
            if self.rotation_task:
                self.rotation_task.cancel()
                try:
                    await self.rotation_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("⏹️ 代理自动轮换已停止")
            
        except Exception as e:
            logger.error(f"❌ 停止代理轮换失败: {e}")
    
    async def _rotation_loop(self):
        """轮换循环"""
        try:
            while self.is_running:
                await asyncio.sleep(self.rotation_interval)
                
                if self.is_running:
                    await self.proxy_manager.rotate_proxy()
                    
        except asyncio.CancelledError:
            logger.info("代理轮换任务已取消")
        except Exception as e:
            logger.error(f"❌ 代理轮换循环出错: {e}")
