#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户代理管理器 - 管理和轮换User-Agent
"""

import random
from typing import List, Optional
from loguru import logger


class UserAgentManager:
    """用户代理管理器类"""
    
    def __init__(self):
        """初始化用户代理管理器"""
        self.user_agents = self._load_user_agents()
        self.current_ua = None
    
    def _load_user_agents(self) -> List[str]:
        """加载用户代理列表"""
        return [
            # Chrome Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            
            # Chrome macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            
            # Firefox Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
            
            # Firefox macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
            
            # Safari macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
            
            # Edge Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            
            # Chrome Linux
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            
            # Mobile Chrome
            'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        ]
    
    def get_random_ua(self) -> str:
        """
        获取随机用户代理
        
        Returns:
            随机用户代理字符串
        """
        try:
            ua = random.choice(self.user_agents)
            self.current_ua = ua
            logger.debug(f"选择用户代理: {ua[:50]}...")
            return ua
        except Exception as e:
            logger.error(f"❌ 获取随机用户代理失败: {e}")
            return self.user_agents[0]  # 返回默认UA
    
    def get_current_ua(self) -> Optional[str]:
        """
        获取当前用户代理
        
        Returns:
            当前用户代理字符串
        """
        return self.current_ua
    
    def get_chrome_ua(self) -> str:
        """
        获取Chrome用户代理
        
        Returns:
            Chrome用户代理字符串
        """
        chrome_uas = [ua for ua in self.user_agents if 'Chrome' in ua and 'Edg' not in ua]
        return random.choice(chrome_uas) if chrome_uas else self.user_agents[0]
    
    def get_firefox_ua(self) -> str:
        """
        获取Firefox用户代理
        
        Returns:
            Firefox用户代理字符串
        """
        firefox_uas = [ua for ua in self.user_agents if 'Firefox' in ua]
        return random.choice(firefox_uas) if firefox_uas else self.user_agents[0]
    
    def get_mobile_ua(self) -> str:
        """
        获取移动端用户代理
        
        Returns:
            移动端用户代理字符串
        """
        mobile_uas = [ua for ua in self.user_agents if 'Mobile' in ua or 'Android' in ua or 'iPhone' in ua]
        return random.choice(mobile_uas) if mobile_uas else self.user_agents[0]
    
    def add_user_agent(self, ua: str):
        """
        添加用户代理
        
        Args:
            ua: 用户代理字符串
        """
        if ua and ua not in self.user_agents:
            self.user_agents.append(ua)
            logger.info(f"✅ 添加用户代理: {ua[:50]}...")
    
    def remove_user_agent(self, ua: str):
        """
        移除用户代理
        
        Args:
            ua: 用户代理字符串
        """
        if ua in self.user_agents:
            self.user_agents.remove(ua)
            logger.info(f"🗑️ 移除用户代理: {ua[:50]}...")
    
    def get_ua_count(self) -> int:
        """
        获取用户代理数量
        
        Returns:
            用户代理数量
        """
        return len(self.user_agents)
    
    def rotate_ua(self) -> str:
        """
        轮换用户代理
        
        Returns:
            新的用户代理字符串
        """
        return self.get_random_ua()
    
    def get_ua_by_browser(self, browser: str) -> str:
        """
        根据浏览器类型获取用户代理
        
        Args:
            browser: 浏览器类型 (chrome, firefox, safari, edge)
            
        Returns:
            对应浏览器的用户代理字符串
        """
        browser = browser.lower()
        
        if browser == 'chrome':
            return self.get_chrome_ua()
        elif browser == 'firefox':
            return self.get_firefox_ua()
        elif browser == 'safari':
            safari_uas = [ua for ua in self.user_agents if 'Safari' in ua and 'Chrome' not in ua]
            return random.choice(safari_uas) if safari_uas else self.user_agents[0]
        elif browser == 'edge':
            edge_uas = [ua for ua in self.user_agents if 'Edg' in ua]
            return random.choice(edge_uas) if edge_uas else self.user_agents[0]
        else:
            return self.get_random_ua()
    
    def get_ua_stats(self) -> dict:
        """
        获取用户代理统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            'total': len(self.user_agents),
            'chrome': len([ua for ua in self.user_agents if 'Chrome' in ua and 'Edg' not in ua]),
            'firefox': len([ua for ua in self.user_agents if 'Firefox' in ua]),
            'safari': len([ua for ua in self.user_agents if 'Safari' in ua and 'Chrome' not in ua]),
            'edge': len([ua for ua in self.user_agents if 'Edg' in ua]),
            'mobile': len([ua for ua in self.user_agents if 'Mobile' in ua or 'Android' in ua or 'iPhone' in ua]),
            'current': self.current_ua
        }
        
        return stats
