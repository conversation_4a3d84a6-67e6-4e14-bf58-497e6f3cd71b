#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码解决器 - 处理各种验证码
基于ddddocr和AI技术
"""

import asyncio
import base64
import io
from typing import Dict, Any, Optional, Tuple
from loguru import logger
from PIL import Image
import ddddocr
from playwright.async_api import Page
import cv2
import numpy as np


class CaptchaSolver:
    """验证码解决器类"""
    
    def __init__(self, captcha_config: Dict[str, Any]):
        """
        初始化验证码解决器
        
        Args:
            captcha_config: 验证码配置
        """
        self.config = captcha_config
        self.ocr = None
        self.slide_detector = None
        self.is_initialized = False
    
    async def initialize(self):
        """初始化验证码解决器"""
        try:
            logger.info("🔧 初始化验证码解决器...")
            
            if self.config.get('ddddocr_enabled', True):
                # 初始化OCR识别器
                self.ocr = ddddocr.DdddOcr(show_ad=False)
                logger.info("✅ OCR识别器初始化完成")
                
                # 初始化滑块检测器
                self.slide_detector = ddddocr.DdddOcr(det=False, ocr=False, show_ad=False)
                logger.info("✅ 滑块检测器初始化完成")
            
            self.is_initialized = True
            logger.success("✅ 验证码解决器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 验证码解决器初始化失败: {e}")
            raise
    
    async def solve_captcha(self, page: Page) -> bool:
        """
        解决验证码
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功解决
        """
        try:
            logger.info("🔐 开始解决验证码...")
            
            # 检测验证码类型
            captcha_type = await self._detect_captcha_type(page)
            
            if captcha_type == 'slider':
                return await self._solve_slider_captcha(page)
            elif captcha_type == 'click':
                return await self._solve_click_captcha(page)
            elif captcha_type == 'text':
                return await self._solve_text_captcha(page)
            else:
                logger.warning("⚠️ 未识别的验证码类型")
                return False
                
        except Exception as e:
            logger.error(f"❌ 解决验证码失败: {e}")
            return False
    
    async def _detect_captcha_type(self, page: Page) -> str:
        """
        检测验证码类型
        
        Args:
            page: 页面对象
            
        Returns:
            验证码类型
        """
        try:
            # 检查滑块验证码
            slider_selectors = [
                '.geetest_slider_button',
                '.slider-button',
                '.captcha-slider',
                '.slide-verify-slider-mask-item'
            ]
            
            for selector in slider_selectors:
                if await page.query_selector(selector):
                    return 'slider'
            
            # 检查点击验证码
            click_selectors = [
                '.geetest_item_wrap',
                '.click-captcha',
                '.verify-img-panel'
            ]
            
            for selector in click_selectors:
                if await page.query_selector(selector):
                    return 'click'
            
            # 检查文字验证码
            text_selectors = [
                'input[type="text"][placeholder*="验证码"]',
                '.captcha-input',
                '.verify-code-input'
            ]
            
            for selector in text_selectors:
                if await page.query_selector(selector):
                    return 'text'
            
            return 'unknown'
            
        except Exception as e:
            logger.error(f"❌ 检测验证码类型失败: {e}")
            return 'unknown'
    
    async def _solve_slider_captcha(self, page: Page) -> bool:
        """
        解决滑块验证码
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功
        """
        try:
            logger.info("🎯 处理滑块验证码...")
            
            # 等待滑块元素加载
            slider_selector = '.geetest_slider_button'
            await page.wait_for_selector(slider_selector, timeout=5000)
            
            # 获取背景图和缺口图
            bg_img, gap_img = await self._get_captcha_images(page)
            
            if not bg_img or not gap_img:
                logger.error("❌ 无法获取验证码图片")
                return False
            
            # 计算滑动距离
            distance = self._calculate_slide_distance(bg_img, gap_img)
            
            if distance <= 0:
                logger.error("❌ 无法计算滑动距离")
                return False
            
            logger.info(f"📏 计算滑动距离: {distance}px")
            
            # 执行滑动
            success = await self._perform_slide(page, slider_selector, distance)
            
            if success:
                logger.success("✅ 滑块验证码解决成功")
            else:
                logger.error("❌ 滑块验证码解决失败")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 解决滑块验证码失败: {e}")
            return False
    
    async def _get_captcha_images(self, page: Page) -> Tuple[Optional[bytes], Optional[bytes]]:
        """
        获取验证码图片
        
        Args:
            page: 页面对象
            
        Returns:
            背景图和缺口图的字节数据
        """
        try:
            # 获取背景图
            bg_selector = '.geetest_canvas_bg canvas'
            bg_element = await page.query_selector(bg_selector)
            
            if not bg_element:
                return None, None
            
            # 截取背景图
            bg_img = await bg_element.screenshot()
            
            # 获取缺口图
            gap_selector = '.geetest_canvas_slice canvas'
            gap_element = await page.query_selector(gap_selector)
            
            if not gap_element:
                return bg_img, None
            
            # 截取缺口图
            gap_img = await gap_element.screenshot()
            
            return bg_img, gap_img
            
        except Exception as e:
            logger.error(f"❌ 获取验证码图片失败: {e}")
            return None, None
    
    def _calculate_slide_distance(self, bg_img: bytes, gap_img: bytes) -> int:
        """
        计算滑动距离
        
        Args:
            bg_img: 背景图字节数据
            gap_img: 缺口图字节数据
            
        Returns:
            滑动距离
        """
        try:
            if not self.slide_detector:
                return 0
            
            # 使用ddddocr计算滑动距离
            distance = self.slide_detector.slide_match(bg_img, gap_img)
            
            return distance
            
        except Exception as e:
            logger.error(f"❌ 计算滑动距离失败: {e}")
            return 0
    
    async def _perform_slide(self, page: Page, slider_selector: str, distance: int) -> bool:
        """
        执行滑动操作
        
        Args:
            page: 页面对象
            slider_selector: 滑块选择器
            distance: 滑动距离
            
        Returns:
            是否成功
        """
        try:
            # 获取滑块元素
            slider = await page.query_selector(slider_selector)
            if not slider:
                return False
            
            # 获取滑块位置
            box = await slider.bounding_box()
            if not box:
                return False
            
            start_x = box['x'] + box['width'] / 2
            start_y = box['y'] + box['height'] / 2
            
            # 生成滑动轨迹
            track = self._generate_slide_track(distance)
            
            # 开始滑动
            await page.mouse.move(start_x, start_y)
            await page.mouse.down()
            
            # 按轨迹滑动
            current_x = start_x
            for move in track:
                current_x += move
                await page.mouse.move(current_x, start_y)
                await asyncio.sleep(0.01)  # 模拟人类滑动速度
            
            # 释放鼠标
            await page.mouse.up()
            
            # 等待验证结果
            await asyncio.sleep(2)
            
            # 检查是否成功
            return await self._check_captcha_success(page)
            
        except Exception as e:
            logger.error(f"❌ 执行滑动操作失败: {e}")
            return False
    
    def _generate_slide_track(self, distance: int) -> list:
        """
        生成滑动轨迹
        
        Args:
            distance: 总距离
            
        Returns:
            滑动轨迹列表
        """
        try:
            track = []
            current = 0
            mid = distance * 4 / 5  # 80%的距离用于加速
            t = 0.2
            v = 0
            
            while current < distance:
                if current < mid:
                    a = 2  # 加速度
                else:
                    a = -3  # 减速度
                
                v0 = v
                v = v0 + a * t
                move = v0 * t + 1 / 2 * a * t * t
                current += move
                track.append(round(move))
            
            return track
            
        except Exception as e:
            logger.error(f"❌ 生成滑动轨迹失败: {e}")
            return [distance]  # 返回简单轨迹
    
    async def _solve_click_captcha(self, page: Page) -> bool:
        """
        解决点击验证码
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功
        """
        try:
            logger.info("👆 处理点击验证码...")
            
            # 这里可以实现点击验证码的逻辑
            # 由于点击验证码比较复杂，暂时返回False让用户手动处理
            logger.warning("⚠️ 点击验证码需要手动处理")
            return False
            
        except Exception as e:
            logger.error(f"❌ 解决点击验证码失败: {e}")
            return False
    
    async def _solve_text_captcha(self, page: Page) -> bool:
        """
        解决文字验证码
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功
        """
        try:
            logger.info("📝 处理文字验证码...")
            
            if not self.ocr:
                return False
            
            # 查找验证码图片
            img_selector = 'img[src*="captcha"], img[src*="verify"], .captcha-img img'
            img_element = await page.query_selector(img_selector)
            
            if not img_element:
                logger.error("❌ 未找到验证码图片")
                return False
            
            # 截取验证码图片
            img_bytes = await img_element.screenshot()
            
            # OCR识别
            result = self.ocr.classification(img_bytes)
            
            if not result:
                logger.error("❌ OCR识别失败")
                return False
            
            logger.info(f"🔍 OCR识别结果: {result}")
            
            # 查找输入框
            input_selector = 'input[type="text"][placeholder*="验证码"], .captcha-input, .verify-code-input'
            input_element = await page.query_selector(input_selector)
            
            if not input_element:
                logger.error("❌ 未找到验证码输入框")
                return False
            
            # 输入验证码
            await input_element.fill(result)
            
            # 提交验证码
            submit_selector = 'button[type="submit"], .submit-btn, .verify-btn'
            submit_element = await page.query_selector(submit_selector)
            
            if submit_element:
                await submit_element.click()
            
            # 等待验证结果
            await asyncio.sleep(2)
            
            return await self._check_captcha_success(page)
            
        except Exception as e:
            logger.error(f"❌ 解决文字验证码失败: {e}")
            return False
    
    async def _check_captcha_success(self, page: Page) -> bool:
        """
        检查验证码是否成功
        
        Args:
            page: 页面对象
            
        Returns:
            是否成功
        """
        try:
            # 检查成功标识
            success_selectors = [
                '.geetest_success',
                '.captcha-success',
                '.verify-success'
            ]
            
            for selector in success_selectors:
                if await page.query_selector(selector):
                    return True
            
            # 检查是否还有验证码元素
            captcha_selectors = [
                '.geetest_holder',
                '.captcha-container',
                '.verify-code'
            ]
            
            for selector in captcha_selectors:
                if await page.query_selector(selector):
                    return False
            
            # 如果没有验证码元素，认为成功
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查验证码状态失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 清理验证码解决器资源...")
            
            self.ocr = None
            self.slide_detector = None
            self.is_initialized = False
            
            logger.info("✅ 验证码解决器资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ 清理验证码解决器资源失败: {e}")
